{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-inter: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/globals.css"], "sourcesContent": [":root {\n  /* 主色调 - 青蓝色系 */\n  --primary-50: #ecfeff;\n  --primary-100: #cffafe;\n  --primary-200: #a5f3fc;\n  --primary-300: #67e8f9;\n  --primary-400: #22d3ee;\n  --primary-500: #06b6d4;\n  --primary-600: #0891b2;\n  --primary-700: #0e7490;\n  --primary-800: #155e75;\n  --primary-900: #164e63;\n\n  /* 中性色 */\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n\n  /* 语义化颜色 */\n  --success: #10b981;\n  --success-light: #d1fae5;\n  --warning: #f59e0b;\n  --warning-light: #fef3c7;\n  --error: #ef4444;\n  --error-light: #fee2e2;\n  --info: #3b82f6;\n  --info-light: #dbeafe;\n\n  /* 背景和前景 */\n  --background: #ffffff;\n  --background-secondary: var(--gray-50);\n  --foreground: var(--gray-900);\n  --foreground-secondary: var(--gray-600);\n\n  /* 主要颜色 */\n  --primary: var(--primary-600);\n  --primary-hover: var(--primary-700);\n  --primary-light: var(--primary-100);\n\n  /* 阴影 */\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n\n  /* 边框半径 */\n  --radius-sm: 0.25rem;\n  --radius: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n  --radius-2xl: 1.5rem;\n\n  /* 过渡动画 */\n  --transition-fast: 150ms ease-in-out;\n  --transition: 200ms ease-in-out;\n  --transition-slow: 300ms ease-in-out;\n}\n\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  line-height: 1.6;\n  font-size: 16px;\n  overflow-x: hidden;\n}\n\n/* 改进的滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: var(--gray-100);\n}\n\n::-webkit-scrollbar-thumb {\n  background: var(--gray-300);\n  border-radius: var(--radius);\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: var(--gray-400);\n}\n\n/* 选择文本样式 */\n::selection {\n  background: var(--primary-200);\n  color: var(--primary-900);\n}\n\n/* 焦点样式 */\n:focus {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n\n:focus:not(:focus-visible) {\n  outline: none;\n}\n\n/* 字体大小系统 */\n.text-xs { font-size: 0.75rem; line-height: 1rem; }\n.text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.text-base { font-size: 1rem; line-height: 1.5rem; }\n.text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.text-2xl { font-size: 1.5rem; line-height: 2rem; }\n.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\n.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }\n.text-5xl { font-size: 3rem; line-height: 1; }\n.text-6xl { font-size: 3.75rem; line-height: 1; }\n\n/* 字体粗细 */\n.font-thin { font-weight: 100; }\n.font-light { font-weight: 300; }\n.font-normal { font-weight: 400; }\n.font-medium { font-weight: 500; }\n.font-semibold { font-weight: 600; }\n.font-bold { font-weight: 700; }\n.font-extrabold { font-weight: 800; }\n.font-black { font-weight: 900; }\n\n/* 基础样式 */\n.min-h-screen {\n  min-height: 100vh;\n}\n\n.flex {\n  display: flex;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.space-x-4 > * + * {\n  margin-left: 1rem;\n}\n\n.space-y-4 > * + * {\n  margin-top: 1rem;\n}\n\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.text-left {\n  text-align: left;\n}\n\n/* 按钮样式系统 */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 0.625rem 1.25rem;\n  border-radius: var(--radius-md);\n  font-weight: 500;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  text-decoration: none;\n  border: 1px solid transparent;\n  cursor: pointer;\n  transition: all var(--transition);\n  position: relative;\n  overflow: hidden;\n  user-select: none;\n  white-space: nowrap;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.btn:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px var(--primary-200);\n}\n\n/* 主要按钮 */\n.btn-primary {\n  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));\n  color: white;\n  box-shadow: var(--shadow);\n}\n\n.btn-primary:hover {\n  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));\n  box-shadow: var(--shadow-md);\n  transform: translateY(-1px);\n}\n\n.btn-primary:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow);\n}\n\n/* 次要按钮 */\n.btn-secondary {\n  background: var(--background);\n  color: var(--gray-700);\n  border-color: var(--gray-300);\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-secondary:hover {\n  background: var(--gray-50);\n  border-color: var(--gray-400);\n  box-shadow: var(--shadow);\n  transform: translateY(-1px);\n}\n\n.btn-secondary:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow-sm);\n}\n\n/* 轮廓按钮 */\n.btn-outline {\n  background: transparent;\n  color: var(--primary-600);\n  border-color: var(--primary-300);\n}\n\n.btn-outline:hover {\n  background: var(--primary-50);\n  border-color: var(--primary-400);\n  color: var(--primary-700);\n}\n\n/* 幽灵按钮 */\n.btn-ghost {\n  background: transparent;\n  color: var(--gray-600);\n  border: none;\n  box-shadow: none;\n}\n\n.btn-ghost:hover {\n  background: var(--gray-100);\n  color: var(--gray-700);\n}\n\n/* 按钮尺寸 */\n.btn-sm {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n\n.btn-lg {\n  padding: 0.75rem 1.5rem;\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n\n.btn-xl {\n  padding: 1rem 2rem;\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n\n/* 全宽按钮 */\n.btn-full {\n  width: 100%;\n}\n\n/* 圆形按钮 */\n.btn-round {\n  border-radius: 9999px;\n}\n\n/* 按钮加载状态 */\n.btn-loading {\n  position: relative;\n  color: transparent;\n}\n\n.btn-loading::after {\n  content: '';\n  position: absolute;\n  width: 1rem;\n  height: 1rem;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n/* 表单和输入框样式 */\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n.form-label {\n  display: block;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--gray-700);\n  margin-bottom: 0.5rem;\n}\n\n.form-label.required::after {\n  content: ' *';\n  color: var(--error);\n}\n\n.input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 1px solid var(--gray-300);\n  border-radius: var(--radius-md);\n  font-size: 1rem;\n  line-height: 1.5;\n  background: var(--background);\n  color: var(--foreground);\n  transition: all var(--transition);\n  box-shadow: var(--shadow-sm);\n}\n\n.input::placeholder {\n  color: var(--gray-400);\n}\n\n.input:hover {\n  border-color: var(--gray-400);\n}\n\n.input:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-200);\n  background: var(--background);\n}\n\n.input:disabled {\n  background: var(--gray-100);\n  color: var(--gray-500);\n  cursor: not-allowed;\n}\n\n/* 输入框状态 */\n.input.error {\n  border-color: var(--error);\n  box-shadow: 0 0 0 3px var(--error-light);\n}\n\n.input.success {\n  border-color: var(--success);\n  box-shadow: 0 0 0 3px var(--success-light);\n}\n\n/* 输入框尺寸 */\n.input-sm {\n  padding: 0.5rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n.input-lg {\n  padding: 1rem 1.25rem;\n  font-size: 1.125rem;\n}\n\n/* 表单错误信息 */\n.form-error {\n  display: block;\n  font-size: 0.75rem;\n  color: var(--error);\n  margin-top: 0.25rem;\n}\n\n.form-success {\n  display: block;\n  font-size: 0.75rem;\n  color: var(--success);\n  margin-top: 0.25rem;\n}\n\n/* 表单帮助文本 */\n.form-help {\n  display: block;\n  font-size: 0.75rem;\n  color: var(--gray-500);\n  margin-top: 0.25rem;\n}\n\n/* 卡片样式系统 */\n.card {\n  background: var(--background);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow);\n  border: 1px solid var(--gray-200);\n  transition: all var(--transition);\n  overflow: hidden;\n}\n\n.card:hover {\n  box-shadow: var(--shadow-md);\n  transform: translateY(-2px);\n}\n\n.card-interactive {\n  cursor: pointer;\n}\n\n.card-interactive:hover {\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-4px);\n  border-color: var(--primary-300);\n}\n\n.card-interactive:active {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n/* 卡片内容区域 */\n.card-header {\n  padding: 1.5rem 1.5rem 0;\n}\n\n.card-body {\n  padding: 1.5rem;\n}\n\n.card-footer {\n  padding: 0 1.5rem 1.5rem;\n  border-top: 1px solid var(--gray-200);\n  margin-top: 1rem;\n  padding-top: 1rem;\n}\n\n/* 卡片变体 */\n.card-elevated {\n  box-shadow: var(--shadow-lg);\n  border: none;\n}\n\n.card-outlined {\n  box-shadow: none;\n  border: 2px solid var(--gray-200);\n}\n\n.card-ghost {\n  background: transparent;\n  box-shadow: none;\n  border: 1px dashed var(--gray-300);\n}\n\n/* 卡片尺寸 */\n.card-sm {\n  border-radius: var(--radius);\n}\n\n.card-sm .card-header,\n.card-sm .card-body,\n.card-sm .card-footer {\n  padding: 1rem;\n}\n\n.card-lg {\n  border-radius: var(--radius-xl);\n}\n\n.card-lg .card-header,\n.card-lg .card-body,\n.card-lg .card-footer {\n  padding: 2rem;\n}\n\n/* 导航栏样式 */\n.nav {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow);\n  border-bottom: 1px solid var(--gray-200);\n  position: sticky;\n  top: 0;\n  z-index: 50;\n  transition: all var(--transition);\n}\n\n.nav-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n}\n\n.nav-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 4rem;\n}\n\n.logo {\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  transition: all var(--transition);\n}\n\n.logo:hover {\n  transform: scale(1.05);\n}\n\n.station-icon {\n  font-size: 2rem;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-3px); }\n}\n\n/* 导航链接样式 */\n.nav-link {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.625rem 1rem;\n  color: var(--gray-600);\n  text-decoration: none;\n  border-radius: var(--radius-md);\n  font-weight: 500;\n  font-size: 0.875rem;\n  transition: all var(--transition);\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));\n  opacity: 0;\n  transition: opacity var(--transition);\n  z-index: -1;\n}\n\n.nav-link:hover {\n  color: var(--primary-700);\n  transform: translateY(-1px);\n}\n\n.nav-link:hover::before {\n  opacity: 1;\n}\n\n.nav-link:active {\n  transform: translateY(0);\n}\n\n.nav-icon {\n  font-size: 1.125rem;\n  transition: transform var(--transition);\n}\n\n.nav-link:hover .nav-icon {\n  transform: scale(1.1);\n}\n\n/* 用户信息样式 */\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.5rem;\n  border-radius: var(--radius-lg);\n  transition: all var(--transition);\n}\n\n.user-info:hover {\n  background: var(--gray-50);\n}\n\n.user-avatar {\n  width: 2.5rem;\n  height: 2.5rem;\n  border-radius: 50%;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: var(--shadow);\n  transition: all var(--transition);\n}\n\n.user-avatar:hover {\n  box-shadow: var(--shadow-md);\n  transform: scale(1.05);\n}\n\n.avatar-img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n/* 移动端菜单按钮 */\n.menu-toggle {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.75rem;\n  border-radius: var(--radius-md);\n  transition: all var(--transition);\n}\n\n.menu-toggle:hover {\n  background: var(--gray-100);\n}\n\n.hamburger {\n  width: 1.5rem;\n  height: 1.2rem;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.hamburger span {\n  display: block;\n  height: 2px;\n  width: 100%;\n  background-color: var(--gray-700);\n  border-radius: 1px;\n  transition: all var(--transition-slow);\n}\n\n.hamburger.open span:nth-child(1) {\n  transform: rotate(45deg) translate(5px, 5px);\n  background-color: var(--primary-600);\n}\n\n.hamburger.open span:nth-child(2) {\n  opacity: 0;\n}\n\n.hamburger.open span:nth-child(3) {\n  transform: rotate(-45deg) translate(7px, -6px);\n  background-color: var(--primary-600);\n}\n\n/* 移动端菜单 */\n.mobile-menu {\n  display: block;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(10px);\n  border-top: 1px solid var(--gray-200);\n  box-shadow: var(--shadow-lg);\n  animation: slideDown var(--transition-slow) ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.mobile-menu-content {\n  padding: 1.5rem;\n}\n\n.mobile-nav-link {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  color: var(--gray-700);\n  text-decoration: none;\n  border-radius: var(--radius-lg);\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  transition: all var(--transition);\n  position: relative;\n  overflow: hidden;\n}\n\n.mobile-nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));\n  opacity: 0;\n  transition: opacity var(--transition);\n  z-index: -1;\n}\n\n.mobile-nav-link:hover {\n  color: var(--primary-700);\n  transform: translateX(4px);\n}\n\n.mobile-nav-link:hover::before {\n  opacity: 1;\n}\n\n.mobile-nav-link .nav-icon {\n  font-size: 1.25rem;\n}\n\n/* 加载动画 */\n.loading-spinner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n}\n\n.spinner {\n  width: 3rem;\n  height: 3rem;\n  border: 3px solid var(--gray-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  box-shadow: var(--shadow);\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 脉冲加载动画 */\n.pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n/* 淡入动画 */\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n/* 滑入动画 */\n.slide-in-left {\n  animation: slideInLeft 0.5s ease-out;\n}\n\n@keyframes slideInLeft {\n  from { opacity: 0; transform: translateX(-20px); }\n  to { opacity: 1; transform: translateX(0); }\n}\n\n.slide-in-right {\n  animation: slideInRight 0.5s ease-out;\n}\n\n@keyframes slideInRight {\n  from { opacity: 0; transform: translateX(20px); }\n  to { opacity: 1; transform: translateX(0); }\n}\n\n/* 响应式隐藏类 */\n.hidden {\n  display: none;\n}\n\n@media (min-width: 768px) {\n  .md-flex {\n    display: flex;\n  }\n\n  .md-hidden {\n    display: none;\n  }\n}\n\n@media (max-width: 640px) {\n  .sm-inline {\n    display: inline;\n  }\n}\n\n/* 主要内容样式 */\n.main-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 3rem 1rem;\n}\n\n.bg-gray-50 {\n  background-color: #f9fafb;\n}\n\n.text-gray-700 {\n  color: #374151;\n}\n\n.text-gray-600 {\n  color: #4b5563;\n}\n\n.text-gray-900 {\n  color: #111827;\n}\n\n.text-4xl {\n  font-size: 2.25rem;\n}\n\n.text-xl {\n  font-size: 1.25rem;\n}\n\n.text-lg {\n  font-size: 1.125rem;\n}\n\n.font-bold {\n  font-weight: 700;\n}\n\n.font-semibold {\n  font-weight: 600;\n}\n\n.mb-8 {\n  margin-bottom: 2rem;\n}\n\n.mb-12 {\n  margin-bottom: 3rem;\n}\n\n.mb-4 {\n  margin-bottom: 1rem;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n\n.p-6 {\n  padding: 1.5rem;\n}\n\n/* Hero 区域样式 */\n.hero {\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 50%, var(--background) 100%);\n  position: relative;\n  padding: 6rem 0;\n  min-height: 80vh;\n  display: flex;\n  align-items: center;\n  overflow: hidden;\n}\n\n.hero::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2306b6d4' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n  animation: float-bg 20s ease-in-out infinite;\n}\n\n@keyframes float-bg {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.hero-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  position: relative;\n  z-index: 1;\n}\n\n.hero-content {\n  text-align: center;\n}\n\n.hero-text {\n  margin-bottom: 4rem;\n}\n\n.hero-title {\n  font-size: 4rem;\n  font-weight: 800;\n  color: var(--gray-900);\n  margin-bottom: 1.5rem;\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n}\n\n.station-highlight {\n  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  position: relative;\n  display: inline-block;\n}\n\n.station-highlight::after {\n  content: '';\n  position: absolute;\n  bottom: -8px;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, var(--primary-400), var(--primary-600));\n  border-radius: 2px;\n  animation: shimmer 2s ease-in-out infinite;\n}\n\n@keyframes shimmer {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 1; }\n}\n\n.hero-description {\n  font-size: 1.5rem;\n  color: var(--gray-600);\n  margin-bottom: 1rem;\n  font-weight: 400;\n}\n\n.hero-subtitle {\n  font-size: 1.125rem;\n  color: var(--gray-500);\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n.hero-actions {\n  margin-top: 3rem;\n}\n\n/* 欢迎回来区域 */\n.welcome-back {\n  margin-bottom: 2rem;\n}\n\n.welcome-title {\n  font-size: 2rem;\n  font-weight: 600;\n  color: #111827;\n  margin-bottom: 0.5rem;\n}\n\n.welcome-subtitle {\n  font-size: 1.125rem;\n  color: #6b7280;\n}\n\n/* 行动卡片 */\n.action-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n  margin-top: 3rem;\n}\n\n.action-card {\n  background: var(--background);\n  padding: 2.5rem;\n  border-radius: var(--radius-2xl);\n  box-shadow: var(--shadow-md);\n  text-decoration: none;\n  transition: all var(--transition-slow);\n  border: 1px solid var(--gray-200);\n  position: relative;\n  overflow: hidden;\n  group: hover;\n}\n\n.action-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));\n  opacity: 0;\n  transition: opacity var(--transition-slow);\n  z-index: 0;\n}\n\n.action-card:hover {\n  transform: translateY(-8px);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--primary-300);\n}\n\n.action-card:hover::before {\n  opacity: 1;\n}\n\n.action-card > * {\n  position: relative;\n  z-index: 1;\n}\n\n.action-icon {\n  font-size: 3.5rem;\n  margin-bottom: 1.5rem;\n  display: block;\n  transition: transform var(--transition);\n}\n\n.action-card:hover .action-icon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.action-title {\n  font-size: 1.375rem;\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: 0.75rem;\n}\n\n.action-description {\n  color: var(--gray-600);\n  line-height: 1.6;\n  font-size: 0.95rem;\n  margin-bottom: 1rem;\n}\n\n.action-arrow {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  background: var(--primary-100);\n  border-radius: 50%;\n  color: var(--primary-600);\n  margin-left: auto;\n  transition: all var(--transition);\n  opacity: 0;\n  transform: translateX(-10px);\n}\n\n.action-card:hover .action-arrow {\n  opacity: 1;\n  transform: translateX(0);\n  background: var(--primary-600);\n  color: white;\n}\n\n/* CTA 区域 */\n.cta-section {\n  margin-bottom: 4rem;\n}\n\n.cta-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--gray-900);\n  margin-bottom: 1rem;\n  letter-spacing: -0.01em;\n}\n\n.cta-subtitle {\n  font-size: 1.25rem;\n  color: var(--gray-600);\n  margin-bottom: 2.5rem;\n  line-height: 1.5;\n}\n\n.cta-buttons {\n  display: flex;\n  gap: 1.5rem;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.btn-large {\n  padding: 1rem 2.5rem;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n/* 预览卡片 */\n.preview-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 2rem;\n}\n\n.preview-card {\n  background: var(--background);\n  padding: 2rem;\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow);\n  transition: all var(--transition-slow);\n  border: 1px solid var(--gray-200);\n  position: relative;\n  overflow: hidden;\n}\n\n.preview-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, var(--gray-50), var(--primary-50));\n  opacity: 0;\n  transition: opacity var(--transition);\n  z-index: 0;\n}\n\n.preview-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--primary-200);\n}\n\n.preview-card:hover::before {\n  opacity: 1;\n}\n\n.preview-card > * {\n  position: relative;\n  z-index: 1;\n}\n\n.preview-icon {\n  font-size: 3rem;\n  margin-bottom: 1.5rem;\n  display: block;\n  transition: transform var(--transition);\n}\n\n.preview-card:hover .preview-icon {\n  transform: scale(1.1);\n}\n\n.preview-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: 0.75rem;\n}\n\n.preview-description {\n  color: var(--gray-600);\n  line-height: 1.6;\n  font-size: 0.9rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .hero {\n    padding: 4rem 0;\n    min-height: 70vh;\n  }\n\n  .hero-title {\n    font-size: 3rem;\n  }\n\n  .action-cards,\n  .preview-cards {\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .hero {\n    padding: 3rem 0;\n    min-height: 60vh;\n  }\n\n  .hero-title {\n    font-size: 2.5rem;\n  }\n\n  .hero-description {\n    font-size: 1.25rem;\n  }\n\n  .welcome-title {\n    font-size: 1.75rem;\n  }\n\n  .cta-title {\n    font-size: 2rem;\n  }\n\n  .cta-buttons {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n\n  .btn-large {\n    width: 100%;\n    max-width: 300px;\n  }\n\n  .action-cards,\n  .preview-cards {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n\n  .nav-content {\n    height: 3.5rem;\n  }\n\n  .logo {\n    font-size: 1.25rem;\n  }\n\n  .station-icon {\n    font-size: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero {\n    padding: 2rem 0;\n  }\n\n  .hero-title {\n    font-size: 2rem;\n  }\n\n  .hero-description {\n    font-size: 1.125rem;\n  }\n\n  .hero-subtitle {\n    font-size: 1rem;\n  }\n\n  .cta-title {\n    font-size: 1.75rem;\n  }\n\n  .action-card,\n  .preview-card {\n    padding: 1.5rem;\n  }\n\n  .action-icon,\n  .preview-icon {\n    font-size: 2.5rem;\n  }\n\n  .mobile-menu-content {\n    padding: 1rem;\n  }\n\n  .mobile-nav-link {\n    padding: 0.75rem;\n  }\n}\n\n/* 实用工具类 */\n.max-w-2xl {\n  max-width: 42rem;\n}\n\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.leading-relaxed {\n  line-height: 1.625;\n}\n\n/* 颜色类 */\n.text-primary-500 { color: var(--primary-500); }\n.text-primary-600 { color: var(--primary-600); }\n.text-primary-700 { color: var(--primary-700); }\n\n/* 间距类 */\n.mb-2 { margin-bottom: 0.5rem; }\n.mb-4 { margin-bottom: 1rem; }\n.mb-6 { margin-bottom: 1.5rem; }\n.mb-8 { margin-bottom: 2rem; }\n.mb-12 { margin-bottom: 3rem; }\n\n.mt-4 { margin-top: 1rem; }\n\n.p-4 { padding: 1rem; }\n.p-6 { padding: 1.5rem; }\n.p-8 { padding: 2rem; }\n\n/* 宽度和高度 */\n.w-4 { width: 1rem; }\n.h-4 { height: 1rem; }\n\n/* 过渡效果 */\n.transition-colors {\n  transition: color var(--transition);\n}\n\n.transition-all {\n  transition: all var(--transition);\n}\n\n/* 状态指示器 */\n.status-dot {\n  width: 0.5rem;\n  height: 0.5rem;\n  border-radius: 50%;\n  display: inline-block;\n}\n\n.status-dot.online {\n  background-color: var(--success);\n}\n\n.status-dot.offline {\n  background-color: var(--gray-400);\n}\n\n.status-dot.busy {\n  background-color: var(--warning);\n}\n\n/* 徽章 */\n.badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  line-height: 1;\n}\n\n.badge-primary {\n  background-color: var(--primary-100);\n  color: var(--primary-800);\n}\n\n.badge-success {\n  background-color: var(--success-light);\n  color: var(--success);\n}\n\n.badge-warning {\n  background-color: var(--warning-light);\n  color: var(--warning);\n}\n\n.badge-error {\n  background-color: var(--error-light);\n  color: var(--error);\n}\n\n/* 分隔线 */\n.divider {\n  height: 1px;\n  background-color: var(--gray-200);\n  margin: 1rem 0;\n}\n\n.divider-vertical {\n  width: 1px;\n  background-color: var(--gray-200);\n  margin: 0 1rem;\n}\n\n/* 工具提示 */\n.tooltip {\n  position: relative;\n  display: inline-block;\n}\n\n.tooltip::after {\n  content: attr(data-tooltip);\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: var(--gray-900);\n  color: white;\n  padding: 0.5rem;\n  border-radius: var(--radius);\n  font-size: 0.75rem;\n  white-space: nowrap;\n  opacity: 0;\n  pointer-events: none;\n  transition: opacity var(--transition);\n  z-index: 1000;\n}\n\n.tooltip:hover::after {\n  opacity: 1;\n}\n\n/* 商品卡片样式 */\n.product-card {\n  background: var(--background);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow);\n  border: 1px solid var(--gray-200);\n  transition: all var(--transition-slow);\n  overflow: hidden;\n}\n\n.product-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--primary-300);\n}\n\n.product-image-container {\n  position: relative;\n  height: 200px;\n  background: var(--gray-100);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.product-image-placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));\n}\n\n.product-tag {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: white;\n}\n\n.tag-hot { background: linear-gradient(135deg, #ef4444, #dc2626); }\n.tag-new { background: linear-gradient(135deg, #10b981, #059669); }\n.tag-recommend { background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); }\n.tag-limited { background: linear-gradient(135deg, #f59e0b, #d97706); }\n\n.product-favorite {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  width: 2rem;\n  height: 2rem;\n  background: rgba(255, 255, 255, 0.9);\n  border: none;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--gray-400);\n  transition: all var(--transition);\n  opacity: 0;\n}\n\n.product-card:hover .product-favorite {\n  opacity: 1;\n}\n\n.product-favorite:hover {\n  color: #ef4444;\n  background: white;\n  box-shadow: var(--shadow);\n}\n\n.product-info {\n  padding: 1.5rem;\n}\n\n.product-name {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--gray-900);\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.product-description {\n  color: var(--gray-600);\n  font-size: 0.875rem;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n}\n\n.product-rating {\n  margin-bottom: 1rem;\n}\n\n.product-price {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.current-price {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: var(--primary-600);\n}\n\n.original-price {\n  font-size: 0.875rem;\n  color: var(--gray-400);\n  text-decoration: line-through;\n}\n\n.product-buy-btn {\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));\n  color: white;\n  border: none;\n  border-radius: var(--radius-md);\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  transition: all var(--transition);\n  cursor: pointer;\n}\n\n.product-buy-btn:hover {\n  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n/* 博客卡片样式 */\n.blog-card {\n  background: var(--background);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow);\n  border: 1px solid var(--gray-200);\n  transition: all var(--transition-slow);\n  overflow: hidden;\n}\n\n.blog-card:hover {\n  transform: translateY(-4px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--primary-300);\n}\n\n.blog-image-placeholder {\n  height: 180px;\n  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.blog-content {\n  padding: 1.5rem;\n}\n\n.blog-category {\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: var(--primary-600);\n  background: var(--primary-100);\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n}\n\n.blog-read-time {\n  font-size: 0.75rem;\n  color: var(--gray-500);\n}\n\n.blog-title {\n  margin-bottom: 0.75rem;\n}\n\n.blog-title a {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--gray-900);\n  text-decoration: none;\n  line-height: 1.4;\n  transition: color var(--transition);\n}\n\n.blog-title a:hover {\n  color: var(--primary-600);\n}\n\n.blog-excerpt {\n  color: var(--gray-600);\n  font-size: 0.875rem;\n  line-height: 1.6;\n  margin-bottom: 1rem;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.blog-meta {\n  padding-top: 1rem;\n  border-top: 1px solid var(--gray-200);\n}\n\n/* 特色文章样式 */\n.featured-post {\n  position: relative;\n}\n\n.featured-image-placeholder {\n  height: 300px;\n  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 联系方式样式 */\n.contact-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n}\n\n.contact-icon {\n  width: 3rem;\n  height: 3rem;\n  background: var(--primary-600);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  flex-shrink: 0;\n}\n\n.contact-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 0.25rem;\n}\n\n.contact-info {\n  font-size: 1rem;\n  color: var(--primary-300);\n  margin-bottom: 0.25rem;\n}\n\n.contact-desc {\n  font-size: 0.875rem;\n  color: var(--gray-400);\n}\n\n/* 全屏登录/注册页面样式 */\n.auth-page {\n  min-height: 100vh;\n  height: 100vh;\n  background: linear-gradient(135deg, var(--primary-50), var(--background), var(--primary-100));\n  overflow: hidden;\n}\n\n.auth-left-panel {\n  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));\n  position: relative;\n  overflow: hidden;\n  height: 100vh;\n}\n\n.auth-decoration {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.05);\n}\n\n.auth-form-container {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: var(--radius-2xl);\n  box-shadow: var(--shadow-xl);\n  border: 1px solid var(--gray-200);\n}\n\n/* 全屏优化 */\n.fullscreen-auth {\n  height: 100vh;\n  overflow: hidden;\n}\n\n.fullscreen-auth .auth-form-side {\n  height: 100vh;\n  overflow-y: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.fullscreen-auth .auth-form-side::-webkit-scrollbar {\n  width: 4px;\n}\n\n.fullscreen-auth .auth-form-side::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb {\n  background: var(--gray-300);\n  border-radius: 2px;\n}\n\n.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb:hover {\n  background: var(--gray-400);\n}\n\n/* 响应式优化 */\n@media (max-width: 1024px) {\n  .fullscreen-auth {\n    height: auto;\n    min-height: 100vh;\n    overflow: visible;\n  }\n\n  .fullscreen-auth .auth-form-side {\n    height: auto;\n    min-height: 100vh;\n    overflow: visible;\n  }\n}\n\n/* 最大宽度工具类 */\n.max-w-7xl {\n  max-width: 80rem;\n}\n\n.max-w-md {\n  max-width: 28rem;\n}\n\n/* 网格布局 */\n.grid {\n  display: grid;\n}\n\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n\n.gap-4 { gap: 1rem; }\n.gap-6 { gap: 1.5rem; }\n.gap-8 { gap: 2rem; }\n.gap-12 { gap: 3rem; }\n\n/* 响应式网格 */\n@media (min-width: 640px) {\n  .responsive-grid-sm {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 768px) {\n  .responsive-grid-md {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .responsive-flex-md {\n    display: flex;\n  }\n\n  .responsive-half-md {\n    width: 50%;\n  }\n}\n\n@media (min-width: 1024px) {\n  .responsive-grid-lg {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .responsive-flex-lg {\n    display: flex;\n  }\n\n  .responsive-half-lg {\n    width: 50%;\n  }\n\n  .responsive-hidden-lg {\n    display: none;\n  }\n}\n\n/* 间距工具类 */\n.px-4 { padding-left: 1rem; padding-right: 1rem; }\n.px-8 { padding-left: 2rem; padding-right: 2rem; }\n.px-12 { padding-left: 3rem; padding-right: 3rem; }\n\n.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }\n.py-8 { padding-top: 2rem; padding-bottom: 2rem; }\n.py-12 { padding-top: 3rem; padding-bottom: 3rem; }\n.py-16 { padding-top: 4rem; padding-bottom: 4rem; }\n\n/* 背景渐变 */\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n\n.from-primary-50 {\n  --tw-gradient-from: var(--primary-50);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 254, 255, 0));\n}\n\n.via-white {\n  --tw-gradient-stops: var(--tw-gradient-from), white, var(--tw-gradient-to, rgba(255, 255, 255, 0));\n}\n\n.to-primary-100 {\n  --tw-gradient-to: var(--primary-100);\n}\n\n/* 文本渐变 */\n.bg-clip-text {\n  -webkit-background-clip: text;\n  background-clip: text;\n}\n\n.text-transparent {\n  color: transparent;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n  color: var(--gray-500);\n}\n\n.empty-state-icon {\n  margin-bottom: 1rem;\n  color: var(--gray-300);\n}\n\n.empty-state-title {\n  font-size: 1.125rem;\n  font-weight: 500;\n  color: var(--gray-700);\n  margin-bottom: 0.5rem;\n}\n\n.empty-state-description {\n  font-size: 0.875rem;\n  color: var(--gray-500);\n  max-width: 24rem;\n  line-height: 1.5;\n}\n\n/* 全栅格跨越 */\n.col-span-full {\n  grid-column: 1 / -1;\n}\n\n/* 登录注册页面增强样式 */\n.auth-page-container {\n  min-height: 100vh;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.auth-left-panel {\n  position: relative;\n  overflow: hidden;\n}\n\n.auth-right-panel {\n  background: rgba(255, 255, 255, 0.5);\n  backdrop-filter: blur(10px);\n}\n\n.auth-form-container {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.auth-input {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.auth-input:focus {\n  transform: translateY(-1px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n\n.auth-button {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.auth-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\n}\n\n.auth-button:active {\n  transform: translateY(0);\n}\n\n/* 浮动动画 */\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.float-animation {\n  animation: float 3s ease-in-out infinite;\n}\n\n/* 脉冲动画增强 */\n@keyframes pulse-enhanced {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n}\n\n.pulse-enhanced {\n  animation: pulse-enhanced 2s ease-in-out infinite;\n}\n\n/* 渐变文字动画 */\n@keyframes gradient-shift {\n  0%, 100% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n}\n\n.gradient-text-animated {\n  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);\n  background-size: 400% 400%;\n  animation: gradient-shift 3s ease infinite;\n  -webkit-background-clip: text;\n  background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n/* 密码强度指示器动画 */\n.password-strength-bar {\n  transition: width 0.5s ease-in-out, background-color 0.3s ease;\n}\n\n/* 表单验证状态 */\n.input-valid {\n  border-color: #10b981;\n  background-color: #f0fdf4;\n}\n\n.input-invalid {\n  border-color: #ef4444;\n  background-color: #fef2f2;\n}\n\n/* 加载状态动画 */\n.loading-dots::after {\n  content: '';\n  animation: loading-dots 1.5s infinite;\n}\n\n@keyframes loading-dots {\n  0%, 20% { content: ''; }\n  40% { content: '.'; }\n  60% { content: '..'; }\n  80%, 100% { content: '...'; }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;;;;;;AAMA;;;;AAKA;;;;AAIA;;;;;;;;;;;AAcA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;AAKA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;AACA;;;;AAGA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;AAKA;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;AAOA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;AAIA;;;;AAMA;;;;AAIA;;;;AAOA;;;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;AAw7CA;;;;;;;;;;AA56CA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAIA;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAMA;;;;AAIA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;;AAMF;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAyBA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAKA;;;;EAIA;;;;;AAMF;;;;AAIA;;;;;AAKA;;;;AAKA;;;;AACA;;;;AACA;;;;AAGA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;AAEA;;;;AACA;;;;AACA;;;;AAGA;;;;AACA;;;;AAGA;;;;AAIA;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;;AAMA;;;;AAIA;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;EACE;;;;;;;AAcF;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;EACE;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAUA;;;;AAKA;;;;;;;;;;;;AAWA;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;AAUA;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAKA", "debugId": null}}]}