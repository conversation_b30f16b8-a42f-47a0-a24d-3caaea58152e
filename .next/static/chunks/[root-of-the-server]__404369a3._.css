/* [next]/internal/font/google/inter_9e72d27f.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_9e72d27f-module__JKMi0a__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_9e72d27f-module__JKMi0a__variable {
  --font-inter: "Inter", "Inter Fallback";
}

/* [project]/src/app/globals.css [app-client] (css) */
:root {
  --primary-50: #ecfeff;
  --primary-100: #cffafe;
  --primary-200: #a5f3fc;
  --primary-300: #67e8f9;
  --primary-400: #22d3ee;
  --primary-500: #06b6d4;
  --primary-600: #0891b2;
  --primary-700: #0e7490;
  --primary-800: #155e75;
  --primary-900: #164e63;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --error: #ef4444;
  --error-light: #fee2e2;
  --info: #3b82f6;
  --info-light: #dbeafe;
  --background: #fff;
  --background-secondary: var(--gray-50);
  --foreground: var(--gray-900);
  --foreground-secondary: var(--gray-600);
  --primary: var(--primary-600);
  --primary-hover: var(--primary-700);
  --primary-light: var(--primary-100);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
  --radius-sm: .25rem;
  --radius: .375rem;
  --radius-md: .5rem;
  --radius-lg: .75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --transition-fast: .15s ease-in-out;
  --transition: .2s ease-in-out;
  --transition-slow: .3s ease-in-out;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:before, :after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  overflow-x: hidden;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

::selection {
  background: var(--primary-200);
  color: var(--primary-900);
}

:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.btn {
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  border: 1px solid rgba(0, 0, 0, 0);
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .625rem 1.25rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  text-decoration: none;
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: .5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  box-shadow: 0 0 0 3px var(--primary-200);
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: #fff;
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  box-shadow: var(--shadow);
  transform: translateY(0);
}

.btn-secondary {
  background: var(--background);
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.btn-secondary:active {
  box-shadow: var(--shadow-sm);
  transform: translateY(0);
}

.btn-outline {
  color: var(--primary-600);
  border-color: var(--primary-300);
  background: none;
}

.btn-outline:hover {
  background: var(--primary-50);
  border-color: var(--primary-400);
  color: var(--primary-700);
}

.btn-ghost {
  color: var(--gray-600);
  box-shadow: none;
  background: none;
  border: none;
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.btn-sm {
  padding: .375rem .75rem;
  font-size: .75rem;
  line-height: 1rem;
}

.btn-lg {
  padding: .75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.btn-full {
  width: 100%;
}

.btn-round {
  border-radius: 9999px;
}

.btn-loading {
  color: rgba(0, 0, 0, 0);
  position: relative;
}

.btn-loading:after {
  content: "";
  border: 2px solid rgba(0, 0, 0, 0);
  border-top-color: currentColor;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  animation: 1s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  color: var(--gray-700);
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  display: block;
}

.form-label.required:after {
  content: " *";
  color: var(--error);
}

.input {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background: var(--background);
  width: 100%;
  color: var(--foreground);
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
  padding: .75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
}

.input::placeholder {
  color: var(--gray-400);
}

.input:hover {
  border-color: var(--gray-400);
}

.input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-200);
  background: var(--background);
  outline: none;
}

.input:disabled {
  background: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

.input.error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px var(--error-light);
}

.input.success {
  border-color: var(--success);
  box-shadow: 0 0 0 3px var(--success-light);
}

.input-sm {
  padding: .5rem .75rem;
  font-size: .875rem;
}

.input-lg {
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
}

.form-error {
  color: var(--error);
  margin-top: .25rem;
  font-size: .75rem;
  display: block;
}

.form-success {
  color: var(--success);
  margin-top: .25rem;
  font-size: .75rem;
  display: block;
}

.form-help {
  color: var(--gray-500);
  margin-top: .25rem;
  font-size: .75rem;
  display: block;
}

.card {
  background: var(--background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
  transform: translateY(-4px);
}

.card-interactive:active {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  padding: 1.5rem 1.5rem 0;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  border-top: 1px solid var(--gray-200);
  margin-top: 1rem;
  padding: 1rem 1.5rem 1.5rem;
}

.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-outlined {
  box-shadow: none;
  border: 2px solid var(--gray-200);
}

.card-ghost {
  box-shadow: none;
  border: 1px dashed var(--gray-300);
  background: none;
}

.card-sm {
  border-radius: var(--radius);
}

.card-sm .card-header, .card-sm .card-body, .card-sm .card-footer {
  padding: 1rem;
}

.card-lg {
  border-radius: var(--radius-xl);
}

.card-lg .card-header, .card-lg .card-body, .card-lg .card-footer {
  padding: 2rem;
}

.nav {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  border-bottom: 1px solid var(--gray-200);
  z-index: 50;
  transition: all var(--transition);
  background: rgba(255, 255, 255, .95);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-content {
  justify-content: space-between;
  align-items: center;
  height: 4rem;
  display: flex;
}

.logo {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-text-fill-color: transparent;
  transition: all var(--transition);
  -webkit-background-clip: text;
  background-clip: text;
  align-items: center;
  gap: .75rem;
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
  display: flex;
}

.logo:hover {
  transform: scale(1.05);
}

.station-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, .1));
  font-size: 2rem;
  animation: 3s ease-in-out infinite float;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.nav-link {
  color: var(--gray-600);
  border-radius: var(--radius-md);
  transition: all var(--transition);
  align-items: center;
  gap: .5rem;
  padding: .625rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.nav-link:before {
  content: "";
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: -1;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.nav-link:hover {
  color: var(--primary-700);
  transform: translateY(-1px);
}

.nav-link:hover:before {
  opacity: 1;
}

.nav-link:active {
  transform: translateY(0);
}

.nav-icon {
  transition: transform var(--transition);
  font-size: 1.125rem;
}

.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

.user-info {
  border-radius: var(--radius-lg);
  transition: all var(--transition);
  align-items: center;
  gap: .75rem;
  padding: .5rem;
  display: flex;
}

.user-info:hover {
  background: var(--gray-50);
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  box-shadow: var(--shadow);
  transition: all var(--transition);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  display: flex;
  overflow: hidden;
}

.user-avatar:hover {
  box-shadow: var(--shadow-md);
  transform: scale(1.05);
}

.avatar-img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: #fff;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
}

.menu-toggle {
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition);
  background: none;
  border: none;
  padding: .75rem;
}

.menu-toggle:hover {
  background: var(--gray-100);
}

.hamburger {
  flex-direction: column;
  justify-content: space-between;
  width: 1.5rem;
  height: 1.2rem;
  display: flex;
  position: relative;
}

.hamburger span {
  background-color: var(--gray-700);
  width: 100%;
  height: 2px;
  transition: all var(--transition-slow);
  border-radius: 1px;
  display: block;
}

.hamburger.open span:first-child {
  background-color: var(--primary-600);
  transform: rotate(45deg)translate(5px, 5px);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  background-color: var(--primary-600);
  transform: rotate(-45deg)translate(7px, -6px);
}

.mobile-menu {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  animation: slideDown var(--transition-slow) ease-out;
  background: rgba(255, 255, 255, .98);
  display: block;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-content {
  padding: 1.5rem;
}

.mobile-nav-link {
  color: var(--gray-700);
  border-radius: var(--radius-lg);
  transition: all var(--transition);
  align-items: center;
  gap: 1rem;
  margin-bottom: .5rem;
  padding: 1rem;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.mobile-nav-link:before {
  content: "";
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: -1;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.mobile-nav-link:hover {
  color: var(--primary-700);
  transform: translateX(4px);
}

.mobile-nav-link:hover:before {
  opacity: 1;
}

.mobile-nav-link .nav-icon {
  font-size: 1.25rem;
}

.loading-spinner {
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  display: flex;
}

.spinner {
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-500);
  width: 3rem;
  height: 3rem;
  box-shadow: var(--shadow);
  border-radius: 50%;
  animation: 1s linear infinite spin;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

.fade-in {
  animation: .5s ease-in-out fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: .5s ease-out slideInLeft;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: .5s ease-out slideInRight;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hidden {
  display: none;
}

@media (min-width: 768px) {
  .md-flex {
    display: flex;
  }

  .md-hidden {
    display: none;
  }
}

@media (max-width: 640px) {
  .sm-inline {
    display: inline;
  }
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-900 {
  color: #111827;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.hero {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 50%, var(--background) 100%);
  align-items: center;
  min-height: 80vh;
  padding: 6rem 0;
  display: flex;
  position: relative;
  overflow: hidden;
}

.hero:before {
  content: "";
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2306b6d4' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: 20s ease-in-out infinite float-bg;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

@keyframes float-bg {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.hero-container {
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
}

.hero-content {
  text-align: center;
}

.hero-text {
  margin-bottom: 4rem;
}

.hero-title {
  color: var(--gray-900);
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
}

.station-highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  display: inline-block;
  position: relative;
}

.station-highlight:after {
  content: "";
  background: linear-gradient(90deg, var(--primary-400), var(--primary-600));
  border-radius: 2px;
  height: 4px;
  animation: 2s ease-in-out infinite shimmer;
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
}

@keyframes shimmer {
  0%, 100% {
    opacity: .7;
  }

  50% {
    opacity: 1;
  }
}

.hero-description {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 400;
}

.hero-subtitle {
  color: var(--gray-500);
  max-width: 600px;
  margin: 0 auto;
  font-size: 1.125rem;
  line-height: 1.6;
}

.hero-actions {
  margin-top: 3rem;
}

.welcome-back {
  margin-bottom: 2rem;
}

.welcome-title {
  color: #111827;
  margin-bottom: .5rem;
  font-size: 2rem;
  font-weight: 600;
}

.welcome-subtitle {
  color: #6b7280;
  font-size: 1.125rem;
}

.action-cards {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  display: grid;
}

.action-card {
  background: var(--background);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  border: 1px solid var(--gray-200);
  group: hover;
  padding: 2.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.action-card:before {
  content: "";
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  opacity: 0;
  transition: opacity var(--transition-slow);
  z-index: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.action-card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
  transform: translateY(-8px);
}

.action-card:hover:before {
  opacity: 1;
}

.action-card > * {
  z-index: 1;
  position: relative;
}

.action-icon {
  transition: transform var(--transition);
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  display: block;
}

.action-card:hover .action-icon {
  transform: scale(1.1)rotate(5deg);
}

.action-title {
  color: var(--gray-900);
  margin-bottom: .75rem;
  font-size: 1.375rem;
  font-weight: 700;
}

.action-description {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: .95rem;
  line-height: 1.6;
}

.action-arrow {
  background: var(--primary-100);
  width: 2rem;
  height: 2rem;
  color: var(--primary-600);
  transition: all var(--transition);
  opacity: 0;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  display: flex;
  transform: translateX(-10px);
}

.action-card:hover .action-arrow {
  opacity: 1;
  background: var(--primary-600);
  color: #fff;
  transform: translateX(0);
}

.cta-section {
  margin-bottom: 4rem;
}

.cta-title {
  color: var(--gray-900);
  letter-spacing: -.01em;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.cta-subtitle {
  color: var(--gray-600);
  margin-bottom: 2.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}

.cta-buttons {
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  display: flex;
}

.btn-large {
  padding: 1rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.preview-cards {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  display: grid;
}

.preview-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  transition: all var(--transition-slow);
  border: 1px solid var(--gray-200);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.preview-card:before {
  content: "";
  background: linear-gradient(135deg, var(--gray-50), var(--primary-50));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.preview-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
  transform: translateY(-4px);
}

.preview-card:hover:before {
  opacity: 1;
}

.preview-card > * {
  z-index: 1;
  position: relative;
}

.preview-icon {
  transition: transform var(--transition);
  margin-bottom: 1.5rem;
  font-size: 3rem;
  display: block;
}

.preview-card:hover .preview-icon {
  transform: scale(1.1);
}

.preview-title {
  color: var(--gray-900);
  margin-bottom: .75rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.preview-description {
  color: var(--gray-600);
  font-size: .9rem;
  line-height: 1.6;
}

@media (max-width: 1024px) {
  .hero {
    min-height: 70vh;
    padding: 4rem 0;
  }

  .hero-title {
    font-size: 3rem;
  }

  .action-cards, .preview-cards {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 60vh;
    padding: 3rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.25rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
  }

  .action-cards, .preview-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nav-content {
    height: 3.5rem;
  }

  .logo {
    font-size: 1.25rem;
  }

  .station-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1.125rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .cta-title {
    font-size: 1.75rem;
  }

  .action-card, .preview-card {
    padding: 1.5rem;
  }

  .action-icon, .preview-icon {
    font-size: 2.5rem;
  }

  .mobile-menu-content {
    padding: 1rem;
  }

  .mobile-nav-link {
    padding: .75rem;
  }
}

.max-w-2xl {
  max-width: 42rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.leading-relaxed {
  line-height: 1.625;
}

.text-primary-500 {
  color: var(--primary-500);
}

.text-primary-600 {
  color: var(--primary-600);
}

.text-primary-700 {
  color: var(--primary-700);
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-4 {
  margin-top: 1rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.transition-colors {
  transition: color var(--transition);
}

.transition-all {
  transition: all var(--transition);
}

.status-dot {
  border-radius: 50%;
  width: .5rem;
  height: .5rem;
  display: inline-block;
}

.status-dot.online {
  background-color: var(--success);
}

.status-dot.offline {
  background-color: var(--gray-400);
}

.status-dot.busy {
  background-color: var(--warning);
}

.badge {
  border-radius: 9999px;
  align-items: center;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 500;
  line-height: 1;
  display: inline-flex;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-error {
  background-color: var(--error-light);
  color: var(--error);
}

.divider {
  background-color: var(--gray-200);
  height: 1px;
  margin: 1rem 0;
}

.divider-vertical {
  background-color: var(--gray-200);
  width: 1px;
  margin: 0 1rem;
}

.tooltip {
  display: inline-block;
  position: relative;
}

.tooltip:after {
  content: attr(data-tooltip);
  background-color: var(--gray-900);
  color: #fff;
  border-radius: var(--radius);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition);
  z-index: 1000;
  padding: .5rem;
  font-size: .75rem;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip:hover:after {
  opacity: 1;
}

.product-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
  transform: translateY(-4px);
}

.product-image-container {
  background: var(--gray-100);
  justify-content: center;
  align-items: center;
  height: 200px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.product-image-placeholder {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.product-tag {
  color: #fff;
  border-radius: 9999px;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 600;
  position: absolute;
  top: 12px;
  left: 12px;
}

.tag-hot {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.tag-new {
  background: linear-gradient(135deg, #10b981, #059669);
}

.tag-recommend {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.tag-limited {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.product-favorite {
  width: 2rem;
  height: 2rem;
  color: var(--gray-400);
  transition: all var(--transition);
  opacity: 0;
  background: rgba(255, 255, 255, .9);
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 12px;
  right: 12px;
}

.product-card:hover .product-favorite {
  opacity: 1;
}

.product-favorite:hover {
  color: #ef4444;
  box-shadow: var(--shadow);
  background: #fff;
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  color: var(--gray-900);
  margin-bottom: .5rem;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
}

.product-description {
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-size: .875rem;
  line-height: 1.5;
}

.product-rating {
  margin-bottom: 1rem;
}

.product-price {
  align-items: center;
  gap: .5rem;
  margin-bottom: 1rem;
  display: flex;
}

.current-price {
  color: var(--primary-600);
  font-size: 1.25rem;
  font-weight: 700;
}

.original-price {
  color: var(--gray-400);
  font-size: .875rem;
  text-decoration: line-through;
}

.product-buy-btn {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: #fff;
  border-radius: var(--radius-md);
  width: 100%;
  transition: all var(--transition);
  cursor: pointer;
  border: none;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .75rem;
  font-weight: 500;
  display: flex;
}

.product-buy-btn:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.blog-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.blog-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
  transform: translateY(-4px);
}

.blog-image-placeholder {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  justify-content: center;
  align-items: center;
  height: 180px;
  display: flex;
}

.blog-content {
  padding: 1.5rem;
}

.blog-category {
  color: var(--primary-600);
  background: var(--primary-100);
  border-radius: 9999px;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 600;
}

.blog-read-time {
  color: var(--gray-500);
  font-size: .75rem;
}

.blog-title {
  margin-bottom: .75rem;
}

.blog-title a {
  color: var(--gray-900);
  transition: color var(--transition);
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
  text-decoration: none;
}

.blog-title a:hover {
  color: var(--primary-600);
}

.blog-excerpt {
  color: var(--gray-600);
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 1rem;
  font-size: .875rem;
  line-height: 1.6;
  display: -webkit-box;
  overflow: hidden;
}

.blog-meta {
  border-top: 1px solid var(--gray-200);
  padding-top: 1rem;
}

.featured-post {
  position: relative;
}

.featured-image-placeholder {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  justify-content: center;
  align-items: center;
  height: 300px;
  display: flex;
}

.contact-item {
  align-items: flex-start;
  gap: 1rem;
  display: flex;
}

.contact-icon {
  background: var(--primary-600);
  border-radius: var(--radius-lg);
  color: #fff;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 3rem;
  height: 3rem;
  display: flex;
}

.contact-title {
  color: #fff;
  margin-bottom: .25rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.contact-info {
  color: var(--primary-300);
  margin-bottom: .25rem;
  font-size: 1rem;
}

.contact-desc {
  color: var(--gray-400);
  font-size: .875rem;
}

.auth-page {
  background: linear-gradient(135deg, var(--primary-50), var(--background), var(--primary-100));
  height: 100vh;
  min-height: 100vh;
  overflow: hidden;
}

.auth-left-panel {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.auth-decoration {
  background: rgba(255, 255, 255, .05);
  border-radius: 50%;
  position: absolute;
}

.auth-form-container {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  background: rgba(255, 255, 255, .95);
}

.fullscreen-auth {
  height: 100vh;
  overflow: hidden;
}

.fullscreen-auth .auth-form-side {
  justify-content: center;
  align-items: center;
  height: 100vh;
  display: flex;
  overflow-y: auto;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar {
  width: 4px;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-track {
  background: none;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 2px;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

@media (max-width: 1024px) {
  .fullscreen-auth, .fullscreen-auth .auth-form-side {
    height: auto;
    min-height: 100vh;
    overflow: visible;
  }
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-md {
  max-width: 28rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-12 {
  gap: 3rem;
}

@media (min-width: 640px) {
  .responsive-grid-sm {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .responsive-grid-md {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .responsive-flex-md {
    display: flex;
  }

  .responsive-half-md {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .responsive-grid-lg {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .responsive-flex-lg {
    display: flex;
  }

  .responsive-half-lg {
    width: 50%;
  }

  .responsive-hidden-lg {
    display: none;
  }
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-primary-50 {
  --tw-gradient-from: var(--primary-50);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 254, 255, 0));
}

.via-white {
  --tw-gradient-stops: var(--tw-gradient-from), white, var(--tw-gradient-to, rgba(255, 255, 255, 0));
}

.to-primary-100 {
  --tw-gradient-to: var(--primary-100);
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-transparent {
  color: rgba(0, 0, 0, 0);
}

.empty-state {
  text-align: center;
  color: var(--gray-500);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
  display: flex;
}

.empty-state-icon {
  color: var(--gray-300);
  margin-bottom: 1rem;
}

.empty-state-title {
  color: var(--gray-700);
  margin-bottom: .5rem;
  font-size: 1.125rem;
  font-weight: 500;
}

.empty-state-description {
  color: var(--gray-500);
  max-width: 24rem;
  font-size: .875rem;
  line-height: 1.5;
}

.col-span-full {
  grid-column: 1 / -1;
}

.auth-page-container {
  height: 100vh;
  min-height: 100vh;
  overflow: hidden;
}

.auth-left-panel {
  position: relative;
  overflow: hidden;
}

.auth-right-panel {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, .5);
}

.auth-form-container {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, .95);
  border: 1px solid rgba(255, 255, 255, .2);
}

.auth-input {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.auth-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, .1);
}

.auth-button {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, .15);
}

.auth-button:active {
  transform: translateY(0);
}

.float-animation {
  animation: 3s ease-in-out infinite float;
}

@keyframes pulse-enhanced {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .7;
    transform: scale(1.05);
  }
}

.pulse-enhanced {
  animation: 2s ease-in-out infinite pulse-enhanced;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981) 0 0 / 400% 400%;
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: 3s infinite gradient-shift;
}

.password-strength-bar {
  transition: width .5s ease-in-out, background-color .3s;
}

.input-valid {
  background-color: #f0fdf4;
  border-color: #10b981;
}

.input-invalid {
  background-color: #fef2f2;
  border-color: #ef4444;
}

.loading-dots:after {
  content: "";
  animation: 1.5s infinite loading-dots;
}

@keyframes loading-dots {
  0%, 20% {
    content: "";
  }

  40% {
    content: ".";
  }

  60% {
    content: "..";
  }

  80%, 100% {
    content: "...";
  }
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__404369a3._.css.map*/