{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { UserLogin } from '@/types'\nimport { \n  Train, \n  ArrowLeft, \n  Mail, \n  Lock, \n  Eye, \n  EyeOff, \n  Sparkles, \n  Star,\n  Circle,\n  Shield,\n  Heart,\n  Zap,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\n\nexport default function LoginPage() {\n  const router = useRouter()\n  const { login } = useAuth()\n  \n  // 表单状态\n  const [formData, setFormData] = useState<UserLogin>({\n    email: '',\n    password: '',\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [focusedField, setFocusedField] = useState<string | null>(null)\n\n  // 表单处理函数\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }))\n    // 清除错误信息\n    if (error) setError('')\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const success = await login(formData)\n      if (success) {\n        router.push('/')\n      } else {\n        setError('登录失败，请检查邮箱和密码')\n      }\n    } catch (err) {\n      setError('登录失败，请稍后重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword)\n  }\n\n  return (\n    <div className=\"min-h-screen h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex overflow-hidden\">\n      {/* 左侧装饰区域 */}\n      <div className=\"hidden lg:flex lg:w-1/2 relative overflow-hidden\">\n        {/* 主背景渐变 */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800\"></div>\n        \n        {/* 动态网格背景 */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute inset-0\" style={{\n            backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px),\n                             radial-gradient(circle at 75% 75%, white 2px, transparent 2px)`,\n            backgroundSize: '60px 60px'\n          }}></div>\n        </div>\n        \n        {/* 浮动装饰元素 */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-20 left-20 w-2 h-2 bg-white bg-opacity-40 rounded-full animate-ping\"></div>\n          <div className=\"absolute top-40 right-32 w-1 h-1 bg-yellow-300 rounded-full animate-pulse\"></div>\n          <div className=\"absolute bottom-32 left-16 w-3 h-3 bg-pink-300 bg-opacity-60 rounded-full animate-bounce\"></div>\n          <div className=\"absolute top-1/3 right-20 w-2 h-2 bg-green-300 bg-opacity-50 rounded-full animate-pulse\"></div>\n          <div className=\"absolute bottom-20 right-40 w-1 h-1 bg-blue-200 rounded-full animate-ping\"></div>\n        </div>\n        \n        {/* 主要内容区域 */}\n        <div className=\"relative z-10 flex flex-col justify-center items-center text-white p-12 h-full\">\n          <div className=\"text-center max-w-lg\">\n            {/* Logo 和动画 */}\n            <div className=\"relative mb-8\">\n              <div className=\"absolute inset-0 bg-white bg-opacity-10 rounded-full blur-xl animate-pulse\"></div>\n              <Train size={80} className=\"relative mx-auto text-white drop-shadow-lg\" />\n              <div className=\"absolute -top-2 -right-2\">\n                <Sparkles size={20} className=\"text-yellow-300 animate-spin\" />\n              </div>\n            </div>\n            \n            <h1 className=\"text-4xl font-bold mb-4 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent\">\n              欢迎回到 Station\n            </h1>\n            <p className=\"text-xl text-blue-100 mb-8 leading-relaxed\">\n              您的个人数字空间，连接生活与创意的美好站点\n            </p>\n            \n            {/* 特色功能展示 */}\n            <div className=\"space-y-4 mb-8\">\n              <div className=\"flex items-center justify-center space-x-3 text-blue-100\">\n                <Shield size={16} className=\"text-green-300\" />\n                <span className=\"text-sm\">安全可靠的用户体验</span>\n              </div>\n              <div className=\"flex items-center justify-center space-x-3 text-blue-100\">\n                <Heart size={16} className=\"text-pink-300\" />\n                <span className=\"text-sm\">精心策划的优质内容</span>\n              </div>\n              <div className=\"flex items-center justify-center space-x-3 text-blue-100\">\n                <Zap size={16} className=\"text-yellow-300\" />\n                <span className=\"text-sm\">快速便捷的购物体验</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* 几何装饰 */}\n        <div className=\"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white to-transparent opacity-5 rounded-full -translate-y-32 translate-x-32 animate-pulse\"></div>\n        <div className=\"absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-white to-transparent opacity-5 rounded-full translate-y-24 -translate-x-24 animate-pulse\"></div>\n        <div className=\"absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-5 rounded-full animate-pulse\"></div>\n      </div>\n\n      {/* 右侧登录表单 */}\n      <div className=\"w-full lg:w-1/2 flex flex-col items-center justify-center p-6 lg:p-12 h-screen overflow-y-auto bg-white bg-opacity-50 backdrop-blur-sm\">\n        <div className=\"w-full max-w-md\">\n          {/* 返回首页按钮 */}\n          <div className=\"mb-8\">\n            <Link href=\"/\" className=\"inline-flex items-center text-gray-500 hover:text-blue-600 transition-all duration-300 text-sm group\">\n              <ArrowLeft size={16} className=\"mr-2 group-hover:-translate-x-1 transition-transform\" />\n              返回首页\n            </Link>\n          </div>\n\n          {/* 移动端 Logo */}\n          <div className=\"lg:hidden text-center mb-8\">\n            <div className=\"inline-flex items-center\">\n              <div className=\"relative\">\n                <Train className=\"text-blue-600 mr-3\" size={32} />\n                <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\">\n                Station\n              </span>\n            </div>\n          </div>\n\n          {/* 表单容器 */}\n          <div className=\"bg-white rounded-3xl shadow-2xl p-8 lg:p-10 border border-gray-100 backdrop-blur-xl bg-opacity-95\">\n            {/* 表单标题 */}\n            <div className=\"text-center mb-8\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl mb-4 shadow-lg\">\n                <Lock className=\"text-white\" size={24} />\n              </div>\n              <h2 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\n                登录账户\n              </h2>\n              <p className=\"text-gray-600\">\n                还没有账户？{' '}\n                <Link href=\"/register\" className=\"font-semibold text-blue-600 hover:text-indigo-600 transition-colors underline decoration-2 underline-offset-2\">\n                  立即注册\n                </Link>\n              </p>\n            </div>\n\n            {/* 登录表单 */}\n            <form className=\"space-y-6\" onSubmit={handleSubmit}>\n              {/* 邮箱输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-700\">\n                  邮箱地址\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <Mail className={`h-5 w-5 transition-colors ${\n                      focusedField === 'email' ? 'text-blue-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    autoComplete=\"email\"\n                    required\n                    className={`w-full pl-12 pr-4 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'email' \n                        ? 'border-blue-500 bg-blue-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"请输入您的邮箱地址\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('email')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                </div>\n              </div>\n              \n              {/* 密码输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-700\">\n                  密码\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <Lock className={`h-5 w-5 transition-colors ${\n                      focusedField === 'password' ? 'text-blue-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    autoComplete=\"current-password\"\n                    required\n                    className={`w-full pl-12 pr-12 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'password' \n                        ? 'border-blue-500 bg-blue-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"请输入您的密码\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('password')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-4 flex items-center\"\n                    onClick={togglePasswordVisibility}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              {/* 错误提示 */}\n              {error && (\n                <div className=\"bg-red-50 border-l-4 border-red-400 p-4 rounded-lg\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <AlertCircle className=\"h-5 w-5 text-red-400\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm text-red-700\">{error}</p>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* 登录按钮 */}\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform ${\n                  loading\n                    ? 'bg-gray-400 cursor-not-allowed'\n                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 hover:scale-105 hover:shadow-lg active:scale-95'\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <Loader2 className=\"animate-spin h-5 w-5 mr-2\" />\n                    登录中...\n                  </div>\n                ) : (\n                  '立即登录'\n                )}\n              </button>\n\n              {/* 忘记密码链接 */}\n              <div className=\"text-center pt-4\">\n                <a href=\"#\" className=\"text-sm text-gray-500 hover:text-blue-600 transition-colors underline decoration-1 underline-offset-2\">\n                  忘记密码？\n                </a>\n              </div>\n            </form>\n          </div>\n          \n          {/* 底部装饰 */}\n          <div className=\"mt-8 text-center\">\n            <div className=\"flex items-center justify-center space-x-2 text-xs text-gray-400\">\n              <Circle size={4} className=\"fill-current\" />\n              <span>安全登录</span>\n              <Circle size={4} className=\"fill-current\" />\n              <span>数据加密</span>\n              <Circle size={4} className=\"fill-current\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAwBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAExB,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,SAAS;QACT,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,UAAU,MAAM,MAAM;YAC5B,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAmB,OAAO;gCACvC,iBAAiB,CAAC;2FAC6D,CAAC;gCAChF,gBAAgB;4BAClB;;;;;;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,4MAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAIlC,8OAAC;oCAAG,WAAU;8CAAgG;;;;;;8CAG9G,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAyD;;;;;;;;;;;;sCAM5F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,QAAK;gDAAC,WAAU;gDAAqB,MAAM;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAK,WAAU;kDAAgG;;;;;;;;;;;;;;;;;sCAOpH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;sDAErC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;;gDAAgB;gDACpB;8DACP,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAgH;;;;;;;;;;;;;;;;;;8CAOrJ,8OAAC;oCAAK,WAAU;oCAAY,UAAU;;sDAEpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA4C;;;;;;8DAG7E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,UAAU,kBAAkB,iBAC7C;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,0IAA0I,EACpJ,iBAAiB,UACb,+BACA,oDACJ;4DACF,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;sDAMpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4C;;;;;;8DAGhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,aAAa,kBAAkB,iBAChD;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,2IAA2I,EACrJ,iBAAiB,aACb,+BACA,oDACJ;4DACF,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;sEAEhC,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS;sEAER,6BACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAOtB,uBACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;sDAO7C,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,2FAA2F,EACrG,UACI,mCACA,wIACJ;sDAED,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;;;;;uDAInD;;;;;;sDAKJ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAwG;;;;;;;;;;;;;;;;;;;;;;;sCAQpI,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}]}