{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ShoppingBag, BookOpen, Ticket, Target, Sparkles, ArrowRight } from 'lucide-react'\n\nexport default function Hero() {\n  const { user } = useAuth()\n\n  return (\n    <section className=\"hero\">\n      <div className=\"hero-container\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1 className=\"hero-title\">\n              欢迎来到 <span className=\"station-highlight\">Station</span>\n            </h1>\n            <p className=\"hero-description\">\n              一个集商品销售和博客于一体的个人站点\n            </p>\n            <p className=\"hero-subtitle\">\n              在这里，您可以发现精选商品，阅读精彩文章，享受独特的购物和阅读体验\n            </p>\n          </div>\n\n          {user ? (\n            <div className=\"hero-actions\">\n              <div className=\"welcome-back\">\n                <h2 className=\"welcome-title\">\n                  欢迎回来，{user.username}！\n                </h2>\n                <p className=\"welcome-subtitle\">\n                  继续您的 Station 之旅\n                </p>\n              </div>\n              \n              <div className=\"action-cards\">\n                <Link href=\"/commodity\" className=\"action-card card-interactive\">\n                  <ShoppingBag className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">商品广场</h3>\n                  <p className=\"action-description\">\n                    浏览精选商品，发现心仪好物\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/books\" className=\"action-card card-interactive\">\n                  <BookOpen className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">站长博客</h3>\n                  <p className=\"action-description\">\n                    阅读最新文章，获取灵感启发\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/ticket-user\" className=\"action-card card-interactive\">\n                  <Ticket className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">我的车票</h3>\n                  <p className=\"action-description\">\n                    查看订单历史，管理购买记录\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n              </div>\n            </div>\n          ) : (\n            <div className=\"hero-actions\">\n              <div className=\"cta-section\">\n                <h2 className=\"cta-title\">\n                  开始您的 Station 之旅\n                </h2>\n                <p className=\"cta-subtitle\">\n                  注册账户，解锁完整功能体验\n                </p>\n                <div className=\"cta-buttons\">\n                  <Link href=\"/register\" className=\"btn btn-primary btn-xl btn-round\">\n                    <Sparkles size={20} />\n                    立即注册\n                  </Link>\n                  <Link href=\"/login\" className=\"btn btn-outline btn-xl btn-round\">\n                    已有账户？登录\n                  </Link>\n                </div>\n              </div>\n              \n              <div className=\"preview-cards\">\n                <div className=\"preview-card\">\n                  <ShoppingBag className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精选商品</h3>\n                  <p className=\"preview-description\">\n                    发现独特好物，享受优质购物体验\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <BookOpen className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精彩博客</h3>\n                  <p className=\"preview-description\">\n                    阅读深度文章，获取知识与灵感\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <Target className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">个性化体验</h3>\n                  <p className=\"preview-description\">\n                    定制专属内容，打造个人空间\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAa;kDACpB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAGhC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAK9B,qBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAgB;4CACtB,KAAK,QAAQ;4CAAC;;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAKlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;0DAChC,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;0DAC5B,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACxC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;;0DAClC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACtC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAM1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAY;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAAe;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;;kEAC/B,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAGxB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACvC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport Navbar from '@/components/Navbar'\nimport Hero from '@/components/Hero'\n\nexport default function Home() {\n  const { loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"loading-spinner\">\n          <div className=\"spinner\"></div>\n          <div className=\"text-lg text-gray-600 mt-4\">加载中...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      <Hero />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,0HAAA,CAAA,UAAI;;;;;;;;;;;AAGX", "debugId": null}}]}