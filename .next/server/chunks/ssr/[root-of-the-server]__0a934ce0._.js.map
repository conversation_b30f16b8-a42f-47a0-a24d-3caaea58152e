{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { UserRegistration } from '@/types'\nimport { \n  Train, \n  ArrowLeft, \n  Mail, \n  User, \n  Lock, \n  Sparkles, \n  Eye, \n  EyeOff, \n  Star,\n  Circle,\n  Shield,\n  Heart,\n  CheckCircle,\n  UserPlus,\n  AlertCircle,\n  Loader2,\n  Check\n} from 'lucide-react'\n\nexport default function RegisterPage() {\n  const router = useRouter()\n  const { register } = useAuth()\n  \n  // 表单状态\n  const [formData, setFormData] = useState<UserRegistration>({\n    email: '',\n    username: '',\n    password: '',\n  })\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [focusedField, setFocusedField] = useState<string | null>(null)\n  const [passwordStrength, setPasswordStrength] = useState(0)\n\n  // 密码强度计算\n  const calculatePasswordStrength = (password: string) => {\n    let strength = 0\n    if (password.length >= 8) strength += 1\n    if (/[a-z]/.test(password)) strength += 1\n    if (/[A-Z]/.test(password)) strength += 1\n    if (/[0-9]/.test(password)) strength += 1\n    if (/[^A-Za-z0-9]/.test(password)) strength += 1\n    return strength\n  }\n\n  // 表单处理函数\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    if (name === 'confirmPassword') {\n      setConfirmPassword(value)\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n      }))\n      \n      // 计算密码强度\n      if (name === 'password') {\n        setPasswordStrength(calculatePasswordStrength(value))\n      }\n    }\n    \n    // 清除错误信息\n    if (error) setError('')\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    // 验证密码匹配\n    if (formData.password !== confirmPassword) {\n      setError('两次输入的密码不匹配')\n      return\n    }\n    \n    // 验证密码强度\n    if (passwordStrength < 3) {\n      setError('密码强度不够，请包含大小写字母、数字和特殊字符')\n      return\n    }\n    \n    setLoading(true)\n    setError('')\n\n    try {\n      const success = await register(formData)\n      if (success) {\n        router.push('/')\n      } else {\n        setError('注册失败，请检查输入信息')\n      }\n    } catch (err) {\n      setError('注册失败，请稍后重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword)\n  }\n\n  const toggleConfirmPasswordVisibility = () => {\n    setShowConfirmPassword(!showConfirmPassword)\n  }\n\n  // 密码强度指示器\n  const getPasswordStrengthColor = () => {\n    if (passwordStrength <= 1) return 'bg-red-500'\n    if (passwordStrength <= 2) return 'bg-yellow-500'\n    if (passwordStrength <= 3) return 'bg-blue-500'\n    return 'bg-green-500'\n  }\n\n  const getPasswordStrengthText = () => {\n    if (passwordStrength <= 1) return '弱'\n    if (passwordStrength <= 2) return '中等'\n    if (passwordStrength <= 3) return '强'\n    return '很强'\n  }\n\n  return (\n    <div className=\"min-h-screen h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-100 flex overflow-hidden\">\n      {/* 左侧装饰区域 */}\n      <div className=\"hidden lg:flex lg:w-1/2 relative overflow-hidden\">\n        {/* 主背景渐变 */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800\"></div>\n        \n        {/* 动态波浪背景 */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"absolute inset-0\" style={{\n            backgroundImage: `repeating-linear-gradient(\n              45deg,\n              transparent,\n              transparent 10px,\n              rgba(255,255,255,0.1) 10px,\n              rgba(255,255,255,0.1) 20px\n            )`\n          }}></div>\n        </div>\n        \n        {/* 浮动装饰元素 */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-16 left-24 w-3 h-3 bg-emerald-300 bg-opacity-60 rounded-full animate-bounce\"></div>\n          <div className=\"absolute top-32 right-20 w-2 h-2 bg-teal-200 rounded-full animate-ping\"></div>\n          <div className=\"absolute bottom-40 left-20 w-4 h-4 bg-cyan-300 bg-opacity-50 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-2/3 right-32 w-2 h-2 bg-emerald-200 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-24 right-16 w-1 h-1 bg-teal-100 rounded-full animate-ping\"></div>\n        </div>\n        \n        {/* 主要内容区域 */}\n        <div className=\"relative z-10 flex flex-col justify-center items-center text-white p-12 h-full\">\n          <div className=\"text-center max-w-lg\">\n            {/* Logo 和动画 */}\n            <div className=\"relative mb-8\">\n              <div className=\"absolute inset-0 bg-white bg-opacity-10 rounded-full blur-xl animate-pulse\"></div>\n              <Train size={80} className=\"relative mx-auto text-white drop-shadow-lg\" />\n              <div className=\"absolute -top-2 -right-2\">\n                <Sparkles size={20} className=\"text-emerald-300 animate-spin\" />\n              </div>\n              <div className=\"absolute -bottom-2 -left-2\">\n                <Star size={16} className=\"text-teal-300 animate-pulse\" />\n              </div>\n            </div>\n            \n            <h1 className=\"text-4xl font-bold mb-4 bg-gradient-to-r from-white to-emerald-100 bg-clip-text text-transparent\">\n              加入 Station\n            </h1>\n            <p className=\"text-xl text-emerald-100 mb-8 leading-relaxed\">\n              开启您的专属数字之旅，探索无限可能\n            </p>\n            \n            {/* 特色功能展示 */}\n            <div className=\"space-y-4 mb-8\">\n              <div className=\"flex items-center justify-center space-x-3 text-emerald-100\">\n                <CheckCircle size={16} className=\"text-emerald-300\" />\n                <span className=\"text-sm\">免费注册，立即开始</span>\n              </div>\n              <div className=\"flex items-center justify-center space-x-3 text-emerald-100\">\n                <Shield size={16} className=\"text-teal-300\" />\n                <span className=\"text-sm\">数据安全，隐私保护</span>\n              </div>\n              <div className=\"flex items-center justify-center space-x-3 text-emerald-100\">\n                <Heart size={16} className=\"text-cyan-300\" />\n                <span className=\"text-sm\">个性化推荐体验</span>\n              </div>\n            </div>\n            \n            {/* 注册步骤指示 */}\n            <div className=\"flex items-center justify-center space-x-2 text-emerald-200\">\n              <div className=\"w-2 h-2 bg-emerald-300 rounded-full\"></div>\n              <div className=\"w-8 h-0.5 bg-emerald-300\"></div>\n              <div className=\"w-2 h-2 bg-emerald-300 rounded-full\"></div>\n              <div className=\"w-8 h-0.5 bg-emerald-300\"></div>\n              <div className=\"w-2 h-2 bg-emerald-300 rounded-full\"></div>\n            </div>\n          </div>\n        </div>\n        \n        {/* 几何装饰 */}\n        <div className=\"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white to-transparent opacity-5 rounded-full -translate-y-32 translate-x-32 animate-pulse\"></div>\n        <div className=\"absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-white to-transparent opacity-5 rounded-full translate-y-24 -translate-x-24 animate-pulse\"></div>\n        <div className=\"absolute top-1/3 right-1/4 w-32 h-32 bg-white opacity-5 rounded-full animate-pulse\"></div>\n      </div>\n\n      {/* 右侧注册表单 */}\n      <div className=\"w-full lg:w-1/2 flex flex-col items-center justify-center p-6 lg:p-12 h-screen overflow-y-auto bg-white bg-opacity-50 backdrop-blur-sm\">\n        <div className=\"w-full max-w-md\">\n          {/* 返回首页按钮 */}\n          <div className=\"mb-8\">\n            <Link href=\"/\" className=\"inline-flex items-center text-gray-500 hover:text-emerald-600 transition-all duration-300 text-sm group\">\n              <ArrowLeft size={16} className=\"mr-2 group-hover:-translate-x-1 transition-transform\" />\n              返回首页\n            </Link>\n          </div>\n\n          {/* 移动端 Logo */}\n          <div className=\"lg:hidden text-center mb-8\">\n            <div className=\"inline-flex items-center\">\n              <div className=\"relative\">\n                <Train className=\"text-emerald-600 mr-3\" size={32} />\n                <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-emerald-400 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent\">\n                Station\n              </span>\n            </div>\n          </div>\n\n          {/* 表单容器 */}\n          <div className=\"bg-white rounded-3xl shadow-2xl p-8 lg:p-10 border border-gray-100 backdrop-blur-xl bg-opacity-95\">\n            {/* 表单标题 */}\n            <div className=\"text-center mb-8\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl mb-4 shadow-lg\">\n                <UserPlus className=\"text-white\" size={24} />\n              </div>\n              <h2 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\n                创建账户\n              </h2>\n              <p className=\"text-gray-600\">\n                已有账户？{' '}\n                <Link href=\"/login\" className=\"font-semibold text-emerald-600 hover:text-teal-600 transition-colors underline decoration-2 underline-offset-2\">\n                  立即登录\n                </Link>\n              </p>\n            </div>\n\n            {/* 注册表单 */}\n            <form className=\"space-y-5\" onSubmit={handleSubmit}>\n              {/* 邮箱输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-700\">\n                  邮箱地址\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <Mail className={`h-5 w-5 transition-colors ${\n                      focusedField === 'email' ? 'text-emerald-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    autoComplete=\"email\"\n                    required\n                    className={`w-full pl-12 pr-4 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'email' \n                        ? 'border-emerald-500 bg-emerald-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"请输入邮箱地址\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('email')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                </div>\n              </div>\n              \n              {/* 用户名输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"username\" className=\"block text-sm font-semibold text-gray-700\">\n                  用户名\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <User className={`h-5 w-5 transition-colors ${\n                      focusedField === 'username' ? 'text-emerald-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"username\"\n                    name=\"username\"\n                    type=\"text\"\n                    autoComplete=\"username\"\n                    required\n                    className={`w-full pl-12 pr-4 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'username' \n                        ? 'border-emerald-500 bg-emerald-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"3-20位字母、数字、下划线\"\n                    value={formData.username}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('username')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                </div>\n                <span className=\"text-xs text-gray-500\">用户名将作为您在 Station 的唯一标识</span>\n              </div>\n              \n              {/* 密码输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-700\">\n                  密码\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <Lock className={`h-5 w-5 transition-colors ${\n                      focusedField === 'password' ? 'text-emerald-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    className={`w-full pl-12 pr-12 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'password' \n                        ? 'border-emerald-500 bg-emerald-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"至少8位，包含字母和数字\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('password')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-4 flex items-center\"\n                    onClick={togglePasswordVisibility}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    )}\n                  </button>\n                </div>\n                \n                {/* 密码强度指示器 */}\n                {formData.password && (\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                        <div \n                          className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}\n                          style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                        ></div>\n                      </div>\n                      <span className={`text-xs font-medium ${\n                        passwordStrength <= 1 ? 'text-red-500' :\n                        passwordStrength <= 2 ? 'text-yellow-500' :\n                        passwordStrength <= 3 ? 'text-blue-500' : 'text-green-500'\n                      }`}>\n                        {getPasswordStrengthText()}\n                      </span>\n                    </div>\n                  </div>\n                )}\n              </div>\n              \n              {/* 确认密码输入 */}\n              <div className=\"space-y-2\">\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-semibold text-gray-700\">\n                  确认密码\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <Lock className={`h-5 w-5 transition-colors ${\n                      focusedField === 'confirmPassword' ? 'text-emerald-500' : 'text-gray-400'\n                    }`} />\n                  </div>\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    className={`w-full pl-12 pr-12 py-4 border-2 rounded-xl text-gray-900 placeholder-gray-500 transition-all duration-300 focus:outline-none focus:ring-0 ${\n                      focusedField === 'confirmPassword' \n                        ? 'border-emerald-500 bg-emerald-50' \n                        : 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    }`}\n                    placeholder=\"请再次输入密码\"\n                    value={confirmPassword}\n                    onChange={handleChange}\n                    onFocus={() => setFocusedField('confirmPassword')}\n                    onBlur={() => setFocusedField(null)}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-4 flex items-center\"\n                    onClick={toggleConfirmPasswordVisibility}\n                  >\n                    {showConfirmPassword ? (\n                      <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\" />\n                    )}\n                  </button>\n                </div>\n                \n                {/* 密码匹配指示 */}\n                {confirmPassword && (\n                  <div className=\"flex items-center space-x-2\">\n                    {formData.password === confirmPassword ? (\n                      <>\n                        <Check className=\"h-4 w-4 text-green-500\" />\n                        <span className=\"text-xs text-green-600\">密码匹配</span>\n                      </>\n                    ) : (\n                      <>\n                        <AlertCircle className=\"h-4 w-4 text-red-500\" />\n                        <span className=\"text-xs text-red-600\">密码不匹配</span>\n                      </>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              {/* 错误提示 */}\n              {error && (\n                <div className=\"bg-red-50 border-l-4 border-red-400 p-4 rounded-lg\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <AlertCircle className=\"h-5 w-5 text-red-400\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm text-red-700\">{error}</p>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* 注册按钮 */}\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform ${\n                  loading\n                    ? 'bg-gray-400 cursor-not-allowed'\n                    : 'bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 hover:scale-105 hover:shadow-lg active:scale-95'\n                }`}\n              >\n                {loading ? (\n                  <div className=\"flex items-center justify-center\">\n                    <Loader2 className=\"animate-spin h-5 w-5 mr-2\" />\n                    创建中...\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-center\">\n                    <Sparkles className=\"h-5 w-5 mr-2\" />\n                    创建账户\n                  </div>\n                )}\n              </button>\n\n              {/* 服务条款 */}\n              <div className=\"text-center pt-4\">\n                <p className=\"text-xs text-gray-500\">\n                  注册即表示您同意我们的\n                  <a href=\"#\" className=\"text-emerald-600 hover:text-teal-600 mx-1 underline\">服务条款</a>\n                  和\n                  <a href=\"#\" className=\"text-emerald-600 hover:text-teal-600 mx-1 underline\">隐私政策</a>\n                </p>\n              </div>\n            </form>\n          </div>\n          \n          {/* 底部装饰 */}\n          <div className=\"mt-8 text-center\">\n            <div className=\"flex items-center justify-center space-x-2 text-xs text-gray-400\">\n              <Circle size={4} className=\"fill-current\" />\n              <span>安全注册</span>\n              <Circle size={4} className=\"fill-current\" />\n              <span>隐私保护</span>\n              <Circle size={4} className=\"fill-current\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AA2Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE3B,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,SAAS;IACT,MAAM,4BAA4B,CAAC;QACjC,IAAI,WAAW;QACf,IAAI,SAAS,MAAM,IAAI,GAAG,YAAY;QACtC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,eAAe,IAAI,CAAC,WAAW,YAAY;QAC/C,OAAO;IACT;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,IAAI,SAAS,mBAAmB;YAC9B,mBAAmB;QACrB,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;YAED,SAAS;YACT,IAAI,SAAS,YAAY;gBACvB,oBAAoB,0BAA0B;YAChD;QACF;QAEA,SAAS;QACT,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,SAAS;QACT,IAAI,SAAS,QAAQ,KAAK,iBAAiB;YACzC,SAAS;YACT;QACF;QAEA,SAAS;QACT,IAAI,mBAAmB,GAAG;YACxB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,UAAU,MAAM,SAAS;YAC/B,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,MAAM,kCAAkC;QACtC,uBAAuB,CAAC;IAC1B;IAEA,UAAU;IACV,MAAM,2BAA2B;QAC/B,IAAI,oBAAoB,GAAG,OAAO;QAClC,IAAI,oBAAoB,GAAG,OAAO;QAClC,IAAI,oBAAoB,GAAG,OAAO;QAClC,OAAO;IACT;IAEA,MAAM,0BAA0B;QAC9B,IAAI,oBAAoB,GAAG,OAAO;QAClC,IAAI,oBAAoB,GAAG,OAAO;QAClC,IAAI,oBAAoB,GAAG,OAAO;QAClC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAmB,OAAO;gCACvC,iBAAiB,CAAC;;;;;;aAMjB,CAAC;4BACJ;;;;;;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,4MAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAI9B,8OAAC;oCAAG,WAAU;8CAAmG;;;;;;8CAGjH,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAK7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAK9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAyD;;;;;;;;;;;;sCAM5F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,QAAK;gDAAC,WAAU;gDAAwB,MAAM;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAK,WAAU;kDAAiG;;;;;;;;;;;;;;;;;sCAOrH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;sDAEzC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;;gDAAgB;gDACrB;8DACN,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAiH;;;;;;;;;;;;;;;;;;8CAOnJ,8OAAC;oCAAK,WAAU;oCAAY,UAAU;;sDAEpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA4C;;;;;;8DAG7E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,UAAU,qBAAqB,iBAChD;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,0IAA0I,EACpJ,iBAAiB,UACb,qCACA,oDACJ;4DACF,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;sDAMpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4C;;;;;;8DAGhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,aAAa,qBAAqB,iBACnD;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,0IAA0I,EACpJ,iBAAiB,aACb,qCACA,oDACJ;4DACF,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;8DAGlC,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA4C;;;;;;8DAGhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,aAAa,qBAAqB,iBACnD;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,2IAA2I,EACrJ,iBAAiB,aACb,qCACA,oDACJ;4DACF,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;sEAEhC,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS;sEAER,6BACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAMpB,SAAS,QAAQ,kBAChB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAW,CAAC,6CAA6C,EAAE,4BAA4B;oEACvF,OAAO;wEAAE,OAAO,GAAG,AAAC,mBAAmB,IAAK,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGvD,8OAAC;gEAAK,WAAW,CAAC,oBAAoB,EACpC,oBAAoB,IAAI,iBACxB,oBAAoB,IAAI,oBACxB,oBAAoB,IAAI,kBAAkB,kBAC1C;0EACC;;;;;;;;;;;;;;;;;;;;;;;sDAQX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA4C;;;;;;8DAGvF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAW,CAAC,0BAA0B,EAC1C,iBAAiB,oBAAoB,qBAAqB,iBAC1D;;;;;;;;;;;sEAEJ,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,sBAAsB,SAAS;4DACrC,cAAa;4DACb,QAAQ;4DACR,WAAW,CAAC,2IAA2I,EACrJ,iBAAiB,oBACb,qCACA,oDACJ;4DACF,aAAY;4DACZ,OAAO;4DACP,UAAU;4DACV,SAAS,IAAM,gBAAgB;4DAC/B,QAAQ,IAAM,gBAAgB;;;;;;sEAEhC,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS;sEAER,oCACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAMpB,iCACC,8OAAC;oDAAI,WAAU;8DACZ,SAAS,QAAQ,KAAK,gCACrB;;0EACE,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAAyB;;;;;;;qFAG3C;;0EACE,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;;wCAQhD,uBACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;sDAO7C,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,2FAA2F,EACrG,UACI,mCACA,0IACJ;sDAED,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;;;;;qEAInD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAO3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDAAwB;kEAEnC,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsD;;;;;;oDAAQ;kEAEpF,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}]}