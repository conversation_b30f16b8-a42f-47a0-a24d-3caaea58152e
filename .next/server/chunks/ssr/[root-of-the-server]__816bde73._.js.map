{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { \n  ShoppingBag, \n  BookOpen, \n  HelpCircle, \n  Ticket, \n  Menu, \n  X,\n  Train,\n  LogOut,\n  User\n} from 'lucide-react'\n\nexport default function Navbar() {\n  const { user, loading, logout } = useAuth()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const navItems = [\n    { name: '商品广场', href: '/commodity', icon: ShoppingBag },\n    { name: '站长博客', href: '/books', icon: BookOpen },\n    { name: '帮助中心', href: '/helps', icon: HelpCircle },\n  ]\n\n  const userNavItems = user ? [\n    { name: '我的车票', href: '/ticket-user', icon: Ticket },\n    { name: '个人中心', href: '/profile', icon: User },\n  ] : []\n\n  return (\n    <nav className=\"nav\">\n      <div className=\"nav-container\">\n        <div className=\"nav-content\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"logo\">\n              <Train className=\"station-icon\" size={28} />\n              Station\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"hidden md-flex items-center space-x-6\">\n              {navItems.map((item) => {\n                const IconComponent = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"nav-link\"\n                  >\n                    <IconComponent className=\"nav-icon\" size={18} />\n                    {item.name}\n                  </Link>\n                )\n              })}\n              \n              {userNavItems.map((item) => {\n                const IconComponent = item.icon\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"nav-link\"\n                  >\n                    <IconComponent className=\"nav-icon\" size={18} />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"spinner w-4 h-4\"></div>\n                  <span className=\"text-gray-500 text-sm\">加载中...</span>\n                </div>\n              ) : user ? (\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"user-info\">\n                    <span className=\"user-avatar\">\n                      {user.avatar_url ? (\n                        <img src={user.avatar_url} alt=\"头像\" className=\"avatar-img\" />\n                      ) : (\n                        <div className=\"avatar-placeholder\">\n                          <User size={16} />\n                        </div>\n                      )}\n                    </span>\n                    <span className=\"hidden sm-inline text-gray-700 font-medium\">\n                      {user.username}\n                    </span>\n                  </div>\n                  <button\n                    onClick={logout}\n                    className=\"btn btn-secondary btn-sm\"\n                    title=\"登出\"\n                  >\n                    <LogOut size={16} />\n                    <span className=\"hidden sm:inline\">登出</span>\n                  </button>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-3\">\n                  <Link href=\"/login\" className=\"btn btn-ghost btn-sm\">\n                    登录\n                  </Link>\n                  <Link href=\"/register\" className=\"btn btn-primary btn-sm\">\n                    注册\n                  </Link>\n                </div>\n              )}\n\n              <button\n                className=\"md-hidden menu-toggle\"\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                aria-label=\"切换菜单\"\n              >\n                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n              </button>\n            </div>\n          </div>\n\n          {isMenuOpen && (\n            <div className=\"mobile-menu\">\n              <div className=\"mobile-menu-content\">\n                {navItems.map((item) => {\n                  const IconComponent = item.icon\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"mobile-nav-link\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      <IconComponent className=\"nav-icon\" size={20} />\n                      {item.name}\n                    </Link>\n                  )\n                })}\n                \n                {userNavItems.map((item) => {\n                  const IconComponent = item.icon\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"mobile-nav-link\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      <IconComponent className=\"nav-icon\" size={20} />\n                      {item.name}\n                    </Link>\n                  )\n                })}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;YAAc,MAAM,oNAAA,CAAA,cAAW;QAAC;QACtD;YAAE,MAAM;YAAQ,MAAM;YAAU,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAC/C;YAAE,MAAM;YAAQ,MAAM;YAAU,MAAM,8NAAA,CAAA,aAAU;QAAC;KAClD;IAED,MAAM,eAAe,OAAO;QAC1B;YAAE,MAAM;YAAQ,MAAM;YAAgB,MAAM,sMAAA,CAAA,SAAM;QAAC;QACnD;YAAE,MAAM;YAAQ,MAAM;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;KAC9C,GAAG,EAAE;IAEN,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,4MAAA,CAAA,QAAK;oCAAC,WAAU;oCAAe,MAAM;;;;;;gCAAM;;;;;;;;;;;;kCAKhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,gBAAgB,KAAK,IAAI;wCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC;oDAAc,WAAU;oDAAW,MAAM;;;;;;gDACzC,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;oCAQpB;oCAEC,aAAa,GAAG,CAAC,CAAC;wCACjB,MAAM,gBAAgB,KAAK,IAAI;wCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC;oDAAc,WAAU;oDAAW,MAAM;;;;;;gDACzC,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;oCAQpB;;;;;;;0CAGF,8OAAC;gCAAI,WAAU;;oCACZ,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;+CAExC,qBACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,KAAK,UAAU,iBACd,8OAAC;4DAAI,KAAK,KAAK,UAAU;4DAAE,KAAI;4DAAK,WAAU;;;;;iFAE9C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;;;;;;kEAIlB,8OAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;;;;;;;0DAGlB,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAM;;kEAEN,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;kEACd,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;6DAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAuB;;;;;;0DAGrD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyB;;;;;;;;;;;;kDAM9D,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,cAAc,CAAC;wCAC9B,cAAW;kDAEV,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;iEAAS,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;oBAKjD,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,gBAAgB,KAAK,IAAI;oCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAc,WAAU;gDAAW,MAAM;;;;;;4CACzC,KAAK,IAAI;;uCANL,KAAK,IAAI;;;;;gCASpB;gCAEC,aAAa,GAAG,CAAC,CAAC;oCACjB,MAAM,gBAAgB,KAAK,IAAI;oCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAc,WAAU;gDAAW,MAAM;;;;;;4CACzC,KAAK,IAAI;;uCANL,KAAK,IAAI;;;;;gCASpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ShoppingBag, BookOpen, Ticket, Target, Sparkles, ArrowRight } from 'lucide-react'\n\nexport default function Hero() {\n  const { user } = useAuth()\n\n  return (\n    <section className=\"hero\">\n      <div className=\"hero-container\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1 className=\"hero-title\">\n              欢迎来到 <span className=\"station-highlight\">Station</span>\n            </h1>\n            <p className=\"hero-description\">\n              一个集商品销售和博客于一体的个人站点\n            </p>\n            <p className=\"hero-subtitle\">\n              在这里，您可以发现精选商品，阅读精彩文章，享受独特的购物和阅读体验\n            </p>\n          </div>\n\n          {user ? (\n            <div className=\"hero-actions\">\n              <div className=\"welcome-back\">\n                <h2 className=\"welcome-title\">\n                  欢迎回来，{user.username}！\n                </h2>\n                <p className=\"welcome-subtitle\">\n                  继续您的 Station 之旅\n                </p>\n              </div>\n              \n              <div className=\"action-cards\">\n                <Link href=\"/commodity\" className=\"action-card card-interactive\">\n                  <ShoppingBag className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">商品广场</h3>\n                  <p className=\"action-description\">\n                    浏览精选商品，发现心仪好物\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/books\" className=\"action-card card-interactive\">\n                  <BookOpen className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">站长博客</h3>\n                  <p className=\"action-description\">\n                    阅读最新文章，获取灵感启发\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/ticket-user\" className=\"action-card card-interactive\">\n                  <Ticket className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">我的车票</h3>\n                  <p className=\"action-description\">\n                    查看订单历史，管理购买记录\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n              </div>\n            </div>\n          ) : (\n            <div className=\"hero-actions\">\n              <div className=\"cta-section\">\n                <h2 className=\"cta-title\">\n                  开始您的 Station 之旅\n                </h2>\n                <p className=\"cta-subtitle\">\n                  注册账户，解锁完整功能体验\n                </p>\n                <div className=\"cta-buttons\">\n                  <Link href=\"/register\" className=\"btn btn-primary btn-xl btn-round\">\n                    <Sparkles size={20} />\n                    立即注册\n                  </Link>\n                  <Link href=\"/login\" className=\"btn btn-outline btn-xl btn-round\">\n                    已有账户？登录\n                  </Link>\n                </div>\n              </div>\n              \n              <div className=\"preview-cards\">\n                <div className=\"preview-card\">\n                  <ShoppingBag className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精选商品</h3>\n                  <p className=\"preview-description\">\n                    发现独特好物，享受优质购物体验\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <BookOpen className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精彩博客</h3>\n                  <p className=\"preview-description\">\n                    阅读深度文章，获取知识与灵感\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <Target className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">个性化体验</h3>\n                  <p className=\"preview-description\">\n                    定制专属内容，打造个人空间\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAa;kDACpB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAGhC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAK9B,qBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAgB;4CACtB,KAAK,QAAQ;4CAAC;;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAKlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;0DAChC,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;0DAC5B,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACxC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;;0DAClC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACtC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAM1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAY;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAAe;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;;kEAC/B,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAGxB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACvC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/ProductSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { ShoppingBag, ArrowRight, Heart } from 'lucide-react'\nimport { Product } from '@/types'\n\n// 商品数据 - 待对接后端API\nconst mockProducts: Product[] = [\n  // 暂时为空，等待后端API对接\n]\n\nexport default function ProductSection() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* 标题区域 */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <ShoppingBag className=\"text-primary-600 mr-3\" size={32} />\n            <h2 className=\"text-3xl font-bold text-gray-900\">精选商品</h2>\n          </div>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            精心挑选的优质商品，为您的生活带来更多便利与美好\n          </p>\n        </div>\n\n        {/* 商品网格 */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {mockProducts.length > 0 ? (\n            mockProducts.map((product) => (\n              <div key={product.id} className=\"product-card group\">\n                <div className=\"product-image-container\">\n                  <div className=\"product-image-placeholder\">\n                    <ShoppingBag size={48} className=\"text-gray-400\" />\n                  </div>\n                  {product.status === 'active' && (\n                    <span className=\"product-tag tag-active\">\n                      在售\n                    </span>\n                  )}\n                  <button className=\"product-favorite\">\n                    <Heart size={16} />\n                  </button>\n                </div>\n\n                <div className=\"product-info\">\n                  <h3 className=\"product-name\">{product.name}</h3>\n                  <p className=\"product-description\">{product.description || '暂无描述'}</p>\n\n                  <div className=\"product-stock\">\n                    <span className=\"text-sm text-gray-500\">\n                      库存: {product.stock}\n                    </span>\n                  </div>\n\n                  <div className=\"product-price\">\n                    <span className=\"current-price\">¥{product.price}</span>\n                  </div>\n\n                  <button className=\"product-buy-btn\">\n                    <ShoppingBag size={16} />\n                    立即购买\n                  </button>\n                </div>\n              </div>\n            ))\n          ) : (\n            // 空状态展示\n            <div className=\"col-span-full empty-state\">\n              <ShoppingBag size={64} className=\"empty-state-icon\" />\n              <h3 className=\"empty-state-title\">暂无商品</h3>\n              <p className=\"empty-state-description\">商品数据正在准备中，敬请期待...</p>\n            </div>\n          )}\n        </div>\n\n        {/* 查看更多按钮 */}\n        <div className=\"text-center\">\n          <Link href=\"/commodity\" className=\"btn btn-outline btn-lg\">\n            查看更多商品\n            <ArrowRight size={20} />\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAJA;;;;AAOA,kBAAkB;AAClB,MAAM,eAA0B,EAE/B;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;oCAAwB,MAAM;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,wBAChB,8OAAC;4BAAqB,WAAU;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;wCAElC,QAAQ,MAAM,KAAK,0BAClB,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;sDAI3C,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB,QAAQ,IAAI;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAAuB,QAAQ,WAAW,IAAI;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAwB;oDACjC,QAAQ,KAAK;;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAgB;oDAAE,QAAQ,KAAK;;;;;;;;;;;;sDAGjD,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;2BA9BrB,QAAQ,EAAE;;;;oCAqCtB,QAAQ;kCACR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CACjC,8OAAC;gCAAG,WAAU;0CAAoB;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;8BAM7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAa,WAAU;;4BAAyB;0CAEzD,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/BlogSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { Book<PERSON><PERSON>, ArrowRight, Calendar, User, Eye } from 'lucide-react'\nimport { BlogPost } from '@/types'\n\n// 博客数据 - 待对接后端API\nconst mockBlogs: BlogPost[] = [\n  // 暂时为空，等待后端API对接\n]\n\nexport default function BlogSection() {\n  const featuredPost = mockBlogs.find(post => post.status === 'published')\n  const regularPosts = mockBlogs.filter(post => post.status === 'published')\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* 标题区域 */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <BookOpen className=\"text-primary-600 mr-3\" size={32} />\n            <h2 className=\"text-3xl font-bold text-gray-900\">站长博客</h2>\n          </div>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            分享生活感悟、创意思考和品质生活的点点滴滴\n          </p>\n        </div>\n\n        {mockBlogs.length > 0 ? (\n          <>\n            {/* 特色文章 */}\n            {featuredPost && (\n              <div className=\"featured-post mb-12\">\n                <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden\">\n                  <div className=\"md:flex\">\n                    <div className=\"md:w-1/2\">\n                      <div className=\"featured-image-placeholder\">\n                        <BookOpen size={64} className=\"text-gray-400\" />\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 p-8\">\n                      <div className=\"flex items-center mb-4\">\n                        <span className=\"badge badge-primary\">特色文章</span>\n                        <span className=\"badge badge-success ml-2\">{featuredPost.category?.name || '未分类'}</span>\n                      </div>\n                      <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                        {featuredPost.title}\n                      </h3>\n                      <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                        {featuredPost.excerpt || '暂无摘要'}\n                      </p>\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <div className=\"flex items-center\">\n                            <User size={14} className=\"mr-1\" />\n                            站长\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar size={14} className=\"mr-1\" />\n                            {featuredPost.published_at || featuredPost.created_at}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Eye size={14} className=\"mr-1\" />\n                            阅读\n                          </div>\n                        </div>\n                      </div>\n                      <Link href={`/books/${featuredPost.id}`} className=\"btn btn-primary\">\n                        阅读全文\n                        <ArrowRight size={16} />\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* 常规文章网格 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n              {regularPosts.map((post) => (\n                <article key={post.id} className=\"blog-card\">\n                  <div className=\"blog-image-placeholder\">\n                    <BookOpen size={40} className=\"text-gray-400\" />\n                  </div>\n\n                  <div className=\"blog-content\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className=\"blog-category\">{post.category?.name || '未分类'}</span>\n                      <span className=\"blog-read-time\">阅读</span>\n                    </div>\n\n                    <h3 className=\"blog-title\">\n                      <Link href={`/books/${post.id}`}>\n                        {post.title}\n                      </Link>\n                    </h3>\n\n                    <p className=\"blog-excerpt\">\n                      {post.excerpt || '暂无摘要'}\n                    </p>\n\n                    <div className=\"blog-meta\">\n                      <div className=\"flex items-center space-x-3 text-sm text-gray-500\">\n                        <div className=\"flex items-center\">\n                          <Calendar size={12} className=\"mr-1\" />\n                          {post.published_at || post.created_at}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Eye size={12} className=\"mr-1\" />\n                          查看\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </>\n        ) : (\n          // 空状态展示\n          <div className=\"empty-state\">\n            <BookOpen size={64} className=\"empty-state-icon\" />\n            <h3 className=\"empty-state-title\">暂无文章</h3>\n            <p className=\"empty-state-description\">博客内容正在准备中，敬请期待...</p>\n          </div>\n        )}\n\n        {/* 查看更多按钮 */}\n        <div className=\"text-center\">\n          <Link href=\"/books\" className=\"btn btn-outline btn-lg\">\n            查看更多文章\n            <ArrowRight size={20} />\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAOA,kBAAkB;AAClB,MAAM,YAAwB,EAE7B;AAEc,SAAS;IACtB,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAC5D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAE9D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAwB,MAAM;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAKxD,UAAU,MAAM,GAAG,kBAClB;;wBAEG,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAK,WAAU;sEAA4B,aAAa,QAAQ,EAAE,QAAQ;;;;;;;;;;;;8DAE7E,8OAAC;oDAAG,WAAU;8DACX,aAAa,KAAK;;;;;;8DAErB,8OAAC;oDAAE,WAAU;8DACV,aAAa,OAAO,IAAI;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAAS;;;;;;;0EAGrC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAC7B,aAAa,YAAY,IAAI,aAAa,UAAU;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;;;;;8DAKxC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE;oDAAE,WAAU;;wDAAkB;sEAEnE,8OAAC,kNAAA,CAAA,aAAU;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS9B,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAGhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAiB,KAAK,QAAQ,EAAE,QAAQ;;;;;;sEACxD,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAGnC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;kEAC5B,KAAK,KAAK;;;;;;;;;;;8DAIf,8OAAC;oDAAE,WAAU;8DACV,KAAK,OAAO,IAAI;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAC7B,KAAK,YAAY,IAAI,KAAK,UAAU;;;;;;;0EAEvC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;;mCA5B9B,KAAK,EAAE;;;;;;;;;;;mCAuC3B,QAAQ;8BACR,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC9B,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;;;;;;;8BAK3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAS,WAAU;;4BAAyB;0CAErD,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/ContactSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Mail, MessageCircle, Phone, MapPin, Clock, Send } from 'lucide-react'\n\nexport default function ContactSection() {\n  return (\n    <section className=\"py-16 bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* 联系信息 */}\n          <div>\n            <h2 className=\"text-3xl font-bold mb-8\">联系我们</h2>\n            <p className=\"text-gray-300 text-lg mb-8 leading-relaxed\">\n              有任何问题或建议？我们很乐意听到您的声音。\n              通过以下方式与我们取得联系，我们会尽快回复您。\n            </p>\n            \n            <div className=\"space-y-6\">\n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <Mail size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">邮箱联系</h3>\n                  <p className=\"contact-info\"><EMAIL></p>\n                  <p className=\"contact-desc\">我们会在24小时内回复您的邮件</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <MessageCircle size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">在线客服</h3>\n                  <p className=\"contact-info\">微信：Station_Support</p>\n                  <p className=\"contact-desc\">工作日 9:00-18:00 在线服务</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <Phone size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">电话咨询</h3>\n                  <p className=\"contact-info\">************</p>\n                  <p className=\"contact-desc\">周一至周五 9:00-18:00</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <MapPin size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">办公地址</h3>\n                  <p className=\"contact-info\">北京市朝阳区创意大厦 808</p>\n                  <p className=\"contact-desc\">欢迎预约到访交流</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* 联系方式说明 */}\n          <div>\n            <div className=\"bg-gray-800 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold mb-6\">联系我们</h3>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                如需更详细的咨询或合作洽谈，请通过左侧提供的联系方式与我们取得联系。\n                我们将竭诚为您提供专业的服务和支持。\n              </p>\n\n              {/* 工作时间提示 */}\n              <div className=\"p-4 bg-primary-900 bg-opacity-50 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <Clock size={16} className=\"text-primary-400 mr-2\" />\n                  <span className=\"text-sm text-gray-300\">\n                    工作时间：周一至周五 9:00-18:00，我们会尽快回复您的消息\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* 底部信息 */}\n        <div className=\"mt-16 pt-8 border-t border-gray-800\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-400 mb-4\">\n              © 2024 Station. All rights reserved.\n            </p>\n            <div className=\"flex items-center justify-center space-x-6 text-sm text-gray-500\">\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">隐私政策</a>\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">服务条款</a>\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">关于我们</a>\n              <a href=\"/helps\" className=\"hover:text-primary-400 transition-colors\">帮助中心</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAEd,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,MAAM;;;;;;;;;;;8DAEvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;;;;;;;8DAEf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;8DAEhB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;sCACC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUlD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpF", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport Navbar from '@/components/Navbar'\nimport Hero from '@/components/Hero'\nimport ProductSection from '@/components/ProductSection'\nimport BlogSection from '@/components/BlogSection'\nimport ContactSection from '@/components/ContactSection'\n\nexport default function Home() {\n  const { loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"loading-spinner\">\n          <div className=\"spinner\"></div>\n          <div className=\"text-lg text-gray-600 mt-4\">加载中...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      <Navbar />\n      <Hero />\n      <ProductSection />\n      <BlogSection />\n      <ContactSection />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,0HAAA,CAAA,UAAI;;;;;0BACL,8OAAC,oIAAA,CAAA,UAAc;;;;;0BACf,8OAAC,iIAAA,CAAA,UAAW;;;;;0BAC<PERSON>,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;AAGrB", "debugId": null}}]}