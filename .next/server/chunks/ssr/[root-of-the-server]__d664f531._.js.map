{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { ShoppingBag, BookOpen, Ticket, Target, Sparkles, ArrowRight } from 'lucide-react'\n\nexport default function Hero() {\n  const { user } = useAuth()\n\n  return (\n    <section className=\"hero\">\n      <div className=\"hero-container\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1 className=\"hero-title\">\n              欢迎来到 <span className=\"station-highlight\">Station</span>\n            </h1>\n            <p className=\"hero-description\">\n              一个集商品销售和博客于一体的个人站点\n            </p>\n            <p className=\"hero-subtitle\">\n              在这里，您可以发现精选商品，阅读精彩文章，享受独特的购物和阅读体验\n            </p>\n          </div>\n\n          {user ? (\n            <div className=\"hero-actions\">\n              <div className=\"welcome-back\">\n                <h2 className=\"welcome-title\">\n                  欢迎回来，{user.username}！\n                </h2>\n                <p className=\"welcome-subtitle\">\n                  继续您的 Station 之旅\n                </p>\n              </div>\n              \n              <div className=\"action-cards\">\n                <Link href=\"/commodity\" className=\"action-card card-interactive\">\n                  <ShoppingBag className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">商品广场</h3>\n                  <p className=\"action-description\">\n                    浏览精选商品，发现心仪好物\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/books\" className=\"action-card card-interactive\">\n                  <BookOpen className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">站长博客</h3>\n                  <p className=\"action-description\">\n                    阅读最新文章，获取灵感启发\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n\n                <Link href=\"/ticket-user\" className=\"action-card card-interactive\">\n                  <Ticket className=\"action-icon\" size={48} />\n                  <h3 className=\"action-title\">我的车票</h3>\n                  <p className=\"action-description\">\n                    查看订单历史，管理购买记录\n                  </p>\n                  <div className=\"action-arrow\">\n                    <ArrowRight size={16} />\n                  </div>\n                </Link>\n              </div>\n            </div>\n          ) : (\n            <div className=\"hero-actions\">\n              <div className=\"cta-section\">\n                <h2 className=\"cta-title\">\n                  开始您的 Station 之旅\n                </h2>\n                <p className=\"cta-subtitle\">\n                  注册账户，解锁完整功能体验\n                </p>\n                <div className=\"cta-buttons\">\n                  <Link href=\"/register\" className=\"btn btn-primary btn-xl btn-round\">\n                    <Sparkles size={20} />\n                    立即注册\n                  </Link>\n                  <Link href=\"/login\" className=\"btn btn-outline btn-xl btn-round\">\n                    已有账户？登录\n                  </Link>\n                </div>\n              </div>\n              \n              <div className=\"preview-cards\">\n                <div className=\"preview-card\">\n                  <ShoppingBag className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精选商品</h3>\n                  <p className=\"preview-description\">\n                    发现独特好物，享受优质购物体验\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <BookOpen className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">精彩博客</h3>\n                  <p className=\"preview-description\">\n                    阅读深度文章，获取知识与灵感\n                  </p>\n                </div>\n\n                <div className=\"preview-card\">\n                  <Target className=\"preview-icon\" size={40} />\n                  <h3 className=\"preview-title\">个性化体验</h3>\n                  <p className=\"preview-description\">\n                    定制专属内容，打造个人空间\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAa;kDACpB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAGhC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAK9B,qBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAgB;4CACtB,KAAK,QAAQ;4CAAC;;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAKlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;0DAChC,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;0DAC5B,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACxC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;;0DAClC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAc,MAAM;;;;;;0DACtC,8OAAC;gDAAG,WAAU;0DAAe;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAM1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAY;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAAe;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;;kEAC/B,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;oDAAM;;;;;;;0DAGxB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAe,MAAM;;;;;;0DACvC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/ProductSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { ShoppingBag, ArrowRight, Star, Heart } from 'lucide-react'\n\n// 模拟商品数据\nconst mockProducts = [\n  {\n    id: 1,\n    name: '精选数码配件',\n    price: 299,\n    originalPrice: 399,\n    image: '/api/placeholder/300/300',\n    rating: 4.8,\n    reviews: 128,\n    tag: '热销',\n    description: '高品质数码配件，提升您的使用体验'\n  },\n  {\n    id: 2,\n    name: '生活美学用品',\n    price: 159,\n    originalPrice: null,\n    image: '/api/placeholder/300/300',\n    rating: 4.9,\n    reviews: 89,\n    tag: '新品',\n    description: '简约设计，为生活增添美感'\n  },\n  {\n    id: 3,\n    name: '创意办公用品',\n    price: 89,\n    originalPrice: 129,\n    image: '/api/placeholder/300/300',\n    rating: 4.7,\n    reviews: 156,\n    tag: '推荐',\n    description: '提升工作效率的创意好物'\n  },\n  {\n    id: 4,\n    name: '健康生活套装',\n    price: 199,\n    originalPrice: null,\n    image: '/api/placeholder/300/300',\n    rating: 4.6,\n    reviews: 73,\n    tag: '限时',\n    description: '关注健康，从生活细节开始'\n  }\n]\n\nexport default function ProductSection() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* 标题区域 */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <ShoppingBag className=\"text-primary-600 mr-3\" size={32} />\n            <h2 className=\"text-3xl font-bold text-gray-900\">精选商品</h2>\n          </div>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            精心挑选的优质商品，为您的生活带来更多便利与美好\n          </p>\n        </div>\n\n        {/* 商品网格 */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {mockProducts.map((product) => (\n            <div key={product.id} className=\"product-card group\">\n              <div className=\"product-image-container\">\n                <div className=\"product-image-placeholder\">\n                  <ShoppingBag size={48} className=\"text-gray-400\" />\n                </div>\n                {product.tag && (\n                  <span className={`product-tag ${product.tag === '热销' ? 'tag-hot' : \n                    product.tag === '新品' ? 'tag-new' : \n                    product.tag === '推荐' ? 'tag-recommend' : 'tag-limited'}`}>\n                    {product.tag}\n                  </span>\n                )}\n                <button className=\"product-favorite\">\n                  <Heart size={16} />\n                </button>\n              </div>\n              \n              <div className=\"product-info\">\n                <h3 className=\"product-name\">{product.name}</h3>\n                <p className=\"product-description\">{product.description}</p>\n                \n                <div className=\"product-rating\">\n                  <div className=\"flex items-center\">\n                    <Star className=\"text-yellow-400 fill-current\" size={14} />\n                    <span className=\"text-sm text-gray-600 ml-1\">\n                      {product.rating} ({product.reviews})\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"product-price\">\n                  <span className=\"current-price\">¥{product.price}</span>\n                  {product.originalPrice && (\n                    <span className=\"original-price\">¥{product.originalPrice}</span>\n                  )}\n                </div>\n                \n                <button className=\"product-buy-btn\">\n                  <ShoppingBag size={16} />\n                  立即购买\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 查看更多按钮 */}\n        <div className=\"text-center\">\n          <Link href=\"/commodity\" className=\"btn btn-outline btn-lg\">\n            查看更多商品\n            <ArrowRight size={20} />\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,SAAS;AACT,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,QAAQ;QACR,SAAS;QACT,KAAK;QACL,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,QAAQ;QACR,SAAS;QACT,KAAK;QACL,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,QAAQ;QACR,SAAS;QACT,KAAK;QACL,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,QAAQ;QACR,SAAS;QACT,KAAK;QACL,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;oCAAwB,MAAM;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;4BAAqB,WAAU;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;wCAElC,QAAQ,GAAG,kBACV,8OAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,QAAQ,GAAG,KAAK,OAAO,YACrD,QAAQ,GAAG,KAAK,OAAO,YACvB,QAAQ,GAAG,KAAK,OAAO,kBAAkB,eAAe;sDACvD,QAAQ,GAAG;;;;;;sDAGhB,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB,QAAQ,IAAI;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAAuB,QAAQ,WAAW;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAA+B,MAAM;;;;;;kEACrD,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,MAAM;4DAAC;4DAAG,QAAQ,OAAO;4DAAC;;;;;;;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAgB;wDAAE,QAAQ,KAAK;;;;;;;gDAC9C,QAAQ,aAAa,kBACpB,8OAAC;oDAAK,WAAU;;wDAAiB;wDAAE,QAAQ,aAAa;;;;;;;;;;;;;sDAI5D,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;2BAtCrB,QAAQ,EAAE;;;;;;;;;;8BA+CxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAa,WAAU;;4BAAyB;0CAEzD,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/BlogSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { BookOpen, ArrowRight, Calendar, User, Eye } from 'lucide-react'\n\n// 模拟博客数据\nconst mockBlogs = [\n  {\n    id: 1,\n    title: '探索现代生活的美学哲学',\n    excerpt: '在快节奏的现代生活中，如何找到属于自己的美学态度？本文将带您探索生活中的美学哲学，发现日常中的诗意瞬间...',\n    author: 'Station 站长',\n    publishDate: '2024-01-15',\n    readTime: '5 分钟',\n    views: 1248,\n    category: '生活美学',\n    image: '/api/placeholder/400/250',\n    featured: true\n  },\n  {\n    id: 2,\n    title: '数字时代的极简主义实践',\n    excerpt: '在信息爆炸的时代，极简主义不仅是一种生活方式，更是一种思维模式。让我们一起探讨如何在数字世界中实践极简...',\n    author: 'Station 站长',\n    publishDate: '2024-01-12',\n    readTime: '8 分钟',\n    views: 892,\n    category: '生活方式',\n    image: '/api/placeholder/400/250',\n    featured: false\n  },\n  {\n    id: 3,\n    title: '创意工作者的灵感来源',\n    excerpt: '灵感从何而来？作为创意工作者，如何在日常生活中捕捉那些稍纵即逝的创意火花？分享一些实用的灵感收集方法...',\n    author: 'Station 站长',\n    publishDate: '2024-01-10',\n    readTime: '6 分钟',\n    views: 756,\n    category: '创意思考',\n    image: '/api/placeholder/400/250',\n    featured: false\n  },\n  {\n    id: 4,\n    title: '品质生活的选择艺术',\n    excerpt: '什么是品质生活？不是昂贵的物品堆砌，而是对生活的用心选择。从日常用品到生活习惯，每一个选择都体现着我们的生活态度...',\n    author: 'Station 站长',\n    publishDate: '2024-01-08',\n    readTime: '7 分钟',\n    views: 1156,\n    category: '品质生活',\n    image: '/api/placeholder/400/250',\n    featured: false\n  }\n]\n\nexport default function BlogSection() {\n  const featuredPost = mockBlogs.find(post => post.featured)\n  const regularPosts = mockBlogs.filter(post => !post.featured)\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* 标题区域 */}\n        <div className=\"text-center mb-12\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <BookOpen className=\"text-primary-600 mr-3\" size={32} />\n            <h2 className=\"text-3xl font-bold text-gray-900\">站长博客</h2>\n          </div>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            分享生活感悟、创意思考和品质生活的点点滴滴\n          </p>\n        </div>\n\n        {/* 特色文章 */}\n        {featuredPost && (\n          <div className=\"featured-post mb-12\">\n            <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden\">\n              <div className=\"md:flex\">\n                <div className=\"md:w-1/2\">\n                  <div className=\"featured-image-placeholder\">\n                    <BookOpen size={64} className=\"text-gray-400\" />\n                  </div>\n                </div>\n                <div className=\"md:w-1/2 p-8\">\n                  <div className=\"flex items-center mb-4\">\n                    <span className=\"badge badge-primary\">特色文章</span>\n                    <span className=\"badge badge-success ml-2\">{featuredPost.category}</span>\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                    {featuredPost.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {featuredPost.excerpt}\n                  </p>\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <User size={14} className=\"mr-1\" />\n                        {featuredPost.author}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Calendar size={14} className=\"mr-1\" />\n                        {featuredPost.publishDate}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Eye size={14} className=\"mr-1\" />\n                        {featuredPost.views}\n                      </div>\n                    </div>\n                  </div>\n                  <Link href={`/books/${featuredPost.id}`} className=\"btn btn-primary\">\n                    阅读全文\n                    <ArrowRight size={16} />\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 常规文章网格 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n          {regularPosts.map((post) => (\n            <article key={post.id} className=\"blog-card\">\n              <div className=\"blog-image-placeholder\">\n                <BookOpen size={40} className=\"text-gray-400\" />\n              </div>\n              \n              <div className=\"blog-content\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"blog-category\">{post.category}</span>\n                  <span className=\"blog-read-time\">{post.readTime}</span>\n                </div>\n                \n                <h3 className=\"blog-title\">\n                  <Link href={`/books/${post.id}`}>\n                    {post.title}\n                  </Link>\n                </h3>\n                \n                <p className=\"blog-excerpt\">\n                  {post.excerpt}\n                </p>\n                \n                <div className=\"blog-meta\">\n                  <div className=\"flex items-center space-x-3 text-sm text-gray-500\">\n                    <div className=\"flex items-center\">\n                      <Calendar size={12} className=\"mr-1\" />\n                      {post.publishDate}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Eye size={12} className=\"mr-1\" />\n                      {post.views}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </article>\n          ))}\n        </div>\n\n        {/* 查看更多按钮 */}\n        <div className=\"text-center\">\n          <Link href=\"/books\" className=\"btn btn-outline btn-lg\">\n            查看更多文章\n            <ArrowRight size={20} />\n          </Link>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,SAAS;AACT,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ;IACzD,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAE5D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAwB,MAAM;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAMxD,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,8OAAC;oDAAK,WAAU;8DAA4B,aAAa,QAAQ;;;;;;;;;;;;sDAEnE,8OAAC;4CAAG,WAAU;sDACX,aAAa,KAAK;;;;;;sDAErB,8OAAC;4CAAE,WAAU;sDACV,aAAa,OAAO;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DACzB,aAAa,MAAM;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAC7B,aAAa,WAAW;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DACxB,aAAa,KAAK;;;;;;;;;;;;;;;;;;sDAIzB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE;4CAAE,WAAU;;gDAAkB;8DAEnE,8OAAC,kNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9B,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAAsB,WAAU;;8CAC/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAGhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB,KAAK,QAAQ;;;;;;8DAC9C,8OAAC;oDAAK,WAAU;8DAAkB,KAAK,QAAQ;;;;;;;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;0DAC5B,KAAK,KAAK;;;;;;;;;;;sDAIf,8OAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAGf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAC7B,KAAK,WAAW;;;;;;;kEAEnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DACxB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;2BA7BP,KAAK,EAAE;;;;;;;;;;8BAuCzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAS,WAAU;;4BAAyB;0CAErD,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/components/ContactSection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Mail, MessageCircle, Phone, MapPin, Clock, Send } from 'lucide-react'\n\nexport default function ContactSection() {\n  return (\n    <section className=\"py-16 bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* 联系信息 */}\n          <div>\n            <h2 className=\"text-3xl font-bold mb-8\">联系我们</h2>\n            <p className=\"text-gray-300 text-lg mb-8 leading-relaxed\">\n              有任何问题或建议？我们很乐意听到您的声音。\n              通过以下方式与我们取得联系，我们会尽快回复您。\n            </p>\n            \n            <div className=\"space-y-6\">\n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <Mail size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">邮箱联系</h3>\n                  <p className=\"contact-info\"><EMAIL></p>\n                  <p className=\"contact-desc\">我们会在24小时内回复您的邮件</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <MessageCircle size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">在线客服</h3>\n                  <p className=\"contact-info\">微信：Station_Support</p>\n                  <p className=\"contact-desc\">工作日 9:00-18:00 在线服务</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <Phone size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">电话咨询</h3>\n                  <p className=\"contact-info\">************</p>\n                  <p className=\"contact-desc\">周一至周五 9:00-18:00</p>\n                </div>\n              </div>\n              \n              <div className=\"contact-item\">\n                <div className=\"contact-icon\">\n                  <MapPin size={20} />\n                </div>\n                <div>\n                  <h3 className=\"contact-title\">办公地址</h3>\n                  <p className=\"contact-info\">北京市朝阳区创意大厦 808</p>\n                  <p className=\"contact-desc\">欢迎预约到访交流</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* 快速联系表单 */}\n          <div>\n            <div className=\"bg-gray-800 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold mb-6\">快速联系</h3>\n              <form className=\"space-y-6\">\n                <div className=\"form-group\">\n                  <label className=\"form-label text-white\">姓名</label>\n                  <input \n                    type=\"text\" \n                    className=\"input bg-gray-700 border-gray-600 text-white placeholder-gray-400\"\n                    placeholder=\"请输入您的姓名\"\n                  />\n                </div>\n                \n                <div className=\"form-group\">\n                  <label className=\"form-label text-white\">邮箱</label>\n                  <input \n                    type=\"email\" \n                    className=\"input bg-gray-700 border-gray-600 text-white placeholder-gray-400\"\n                    placeholder=\"请输入您的邮箱\"\n                  />\n                </div>\n                \n                <div className=\"form-group\">\n                  <label className=\"form-label text-white\">主题</label>\n                  <select className=\"input bg-gray-700 border-gray-600 text-white\">\n                    <option value=\"\">请选择咨询主题</option>\n                    <option value=\"product\">商品咨询</option>\n                    <option value=\"order\">订单问题</option>\n                    <option value=\"technical\">技术支持</option>\n                    <option value=\"cooperation\">合作洽谈</option>\n                    <option value=\"other\">其他问题</option>\n                  </select>\n                </div>\n                \n                <div className=\"form-group\">\n                  <label className=\"form-label text-white\">留言内容</label>\n                  <textarea \n                    rows={4}\n                    className=\"input bg-gray-700 border-gray-600 text-white placeholder-gray-400 resize-none\"\n                    placeholder=\"请详细描述您的问题或建议...\"\n                  ></textarea>\n                </div>\n                \n                <button type=\"submit\" className=\"btn btn-primary btn-full\">\n                  <Send size={16} />\n                  发送消息\n                </button>\n              </form>\n            </div>\n            \n            {/* 工作时间提示 */}\n            <div className=\"mt-6 p-4 bg-primary-900 bg-opacity-50 rounded-lg\">\n              <div className=\"flex items-center\">\n                <Clock size={16} className=\"text-primary-400 mr-2\" />\n                <span className=\"text-sm text-gray-300\">\n                  工作时间：周一至周五 9:00-18:00，我们会尽快回复您的消息\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* 底部信息 */}\n        <div className=\"mt-16 pt-8 border-t border-gray-800\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-400 mb-4\">\n              © 2024 Station. All rights reserved.\n            </p>\n            <div className=\"flex items-center justify-center space-x-6 text-sm text-gray-500\">\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">隐私政策</a>\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">服务条款</a>\n              <a href=\"#\" className=\"hover:text-primary-400 transition-colors\">关于我们</a>\n              <a href=\"/helps\" className=\"hover:text-primary-400 transition-colors\">帮助中心</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAEd,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,MAAM;;;;;;;;;;;8DAEvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;;;;;;;8DAEf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;8DAEhB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;sEAC5B,8OAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAwB;;;;;;sEACzC,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAwB;;;;;;sEACzC,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAwB;;;;;;sEACzC,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;8DAI1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAwB;;;;;;sEACzC,8OAAC;4DACC,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;oDAAO,MAAK;oDAAS,WAAU;;sEAC9B,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;8CAOxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAShD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2C;;;;;;kDACjE,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpF", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport Navbar from '@/components/Navbar'\nimport Hero from '@/components/Hero'\nimport ProductSection from '@/components/ProductSection'\nimport BlogSection from '@/components/BlogSection'\nimport ContactSection from '@/components/ContactSection'\n\nexport default function Home() {\n  const { loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"loading-spinner\">\n          <div className=\"spinner\"></div>\n          <div className=\"text-lg text-gray-600 mt-4\">加载中...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      <Navbar />\n      <Hero />\n      <ProductSection />\n      <BlogSection />\n      <ContactSection />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,0HAAA,CAAA,UAAI;;;;;0BACL,8OAAC,oIAAA,CAAA,UAAc;;;;;0BACf,8OAAC,iIAAA,CAAA,UAAW;;;;;0BAC<PERSON>,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;;AAGrB", "debugId": null}}]}