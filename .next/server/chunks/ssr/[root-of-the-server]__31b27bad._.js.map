{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { UserRegistration } from '@/types'\nimport Navbar from '@/components/Navbar'\n\nexport default function RegisterPage() {\n  const router = useRouter()\n  const { register } = useAuth()\n  const [formData, setFormData] = useState<UserRegistration>({\n    email: '',\n    username: '',\n    password: '',\n  })\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    if (name === 'confirmPassword') {\n      setConfirmPassword(value)\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n      }))\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    // 验证密码确认\n    if (formData.password !== confirmPassword) {\n      setError('两次输入的密码不一致')\n      setLoading(false)\n      return\n    }\n\n    try {\n      const success = await register(formData)\n      if (success) {\n        router.push('/login?message=注册成功，请登录')\n      } else {\n        setError('注册失败，请检查输入信息')\n      }\n    } catch (err) {\n      setError('注册失败，请稍后重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full\">\n        <div className=\"card card-lg fade-in\">\n          <div className=\"card-body\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                加入 Station\n              </h2>\n              <p className=\"text-gray-600\">\n                已有账户？{' '}\n                <Link href=\"/login\" className=\"font-medium text-primary-600 hover:text-primary-700 transition-colors\">\n                  立即登录\n                </Link>\n              </p>\n            </div>\n            <form className=\"space-y-6\" onSubmit={handleSubmit}>\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label required\">\n                  邮箱地址\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"input\"\n                  placeholder=\"请输入邮箱地址\"\n                  value={formData.email}\n                  onChange={handleChange}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"username\" className=\"form-label required\">\n                  用户名\n                </label>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  autoComplete=\"username\"\n                  required\n                  className=\"input\"\n                  placeholder=\"3-20位字母、数字、下划线\"\n                  value={formData.username}\n                  onChange={handleChange}\n                />\n                <span className=\"form-help\">用户名将作为您在 Station 的唯一标识</span>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\" className=\"form-label required\">\n                  密码\n                </label>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"input\"\n                  placeholder=\"至少8位，包含字母和数字\"\n                  value={formData.password}\n                  onChange={handleChange}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"confirmPassword\" className=\"form-label required\">\n                  确认密码\n                </label>\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  className=\"input\"\n                  placeholder=\"请再次输入密码\"\n                  value={confirmPassword}\n                  onChange={handleChange}\n                />\n              </div>\n\n              {error && (\n                <div className=\"form-error text-center\">\n                  {error}\n                </div>\n              )}\n\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`btn btn-primary btn-full btn-lg ${loading ? 'btn-loading' : ''}`}\n              >\n                {loading ? '注册中...' : '创建账户'}\n              </button>\n\n              <div className=\"text-center\">\n                <Link href=\"/\" className=\"text-sm text-primary-600 hover:text-primary-700 transition-colors\">\n                  返回 Station\n                </Link>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,IAAI,SAAS,mBAAmB;YAC9B,mBAAmB;QACrB,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,SAAS;QACT,IAAI,SAAS,QAAQ,KAAK,iBAAiB;YACzC,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,UAAU,MAAM,SAAS;YAC/B,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;;gDAAgB;gDACrB;8DACN,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAwE;;;;;;;;;;;;;;;;;;8CAK1G,8OAAC;oCAAK,WAAU;oCAAY,UAAU;;sDACpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAsB;;;;;;8DAGvD,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,KAAK;oDACrB,UAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAsB;;;;;;8DAG1D,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;sDAG9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAsB;;;;;;8DAG1D,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAAsB;;;;;;8DAGjE,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU;;;;;;;;;;;;wCAIb,uBACC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,gCAAgC,EAAE,UAAU,gBAAgB,IAAI;sDAE3E,UAAU,WAAW;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7G", "debugId": null}}]}