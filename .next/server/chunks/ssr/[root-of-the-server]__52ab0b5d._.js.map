{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal-station/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/hooks/useAuth'\nimport { UserLogin } from '@/types'\nimport Navbar from '@/components/Navbar'\n\nexport default function LoginPage() {\n  const router = useRouter()\n  const { login } = useAuth()\n  const [formData, setFormData] = useState<UserLogin>({\n    email: '',\n    password: '',\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const success = await login(formData)\n      if (success) {\n        router.push('/')\n      } else {\n        setError('登录失败，请检查邮箱和密码')\n      }\n    } catch (err) {\n      setError('登录失败，请稍后重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full\">\n        <div className=\"card card-lg fade-in\">\n          <div className=\"card-body\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                欢迎回到 Station\n              </h2>\n              <p className=\"text-gray-600\">\n                还没有账户？{' '}\n                <Link href=\"/register\" className=\"font-medium text-primary-600 hover:text-primary-700 transition-colors\">\n                  立即注册\n                </Link>\n              </p>\n            </div>\n            <form className=\"space-y-6\" onSubmit={handleSubmit}>\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  邮箱地址\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"input\"\n                  placeholder=\"请输入您的邮箱地址\"\n                  value={formData.email}\n                  onChange={handleChange}\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"password\" className=\"form-label\">\n                  密码\n                </label>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  className=\"input\"\n                  placeholder=\"请输入您的密码\"\n                  value={formData.password}\n                  onChange={handleChange}\n                />\n              </div>\n\n              {error && (\n                <div className=\"form-error text-center\">\n                  {error}\n                </div>\n              )}\n\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`btn btn-primary btn-full btn-lg ${loading ? 'btn-loading' : ''}`}\n              >\n                {loading ? '登录中...' : '登录'}\n              </button>\n\n              <div className=\"text-center\">\n                <Link href=\"/\" className=\"text-sm text-primary-600 hover:text-primary-700 transition-colors\">\n                  返回 Station\n                </Link>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,UAAU,MAAM,MAAM;YAC5B,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;;gDAAgB;gDACpB;8DACP,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAwE;;;;;;;;;;;;;;;;;;8CAK7G,8OAAC;oCAAK,WAAU;oCAAY,UAAU;;sDACpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAa;;;;;;8DAG9C,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,KAAK;oDACrB,UAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAa;;;;;;8DAGjD,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;;;;;;;wCAIb,uBACC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;sDAIL,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,gCAAgC,EAAE,UAAU,gBAAgB,IAAI;sDAE3E,UAAU,WAAW;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7G", "debugId": null}}]}