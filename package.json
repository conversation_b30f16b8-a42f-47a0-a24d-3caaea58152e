{"name": "station", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}