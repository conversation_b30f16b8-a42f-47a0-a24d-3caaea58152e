// API 工具函数 - 为后期对接后端API做准备

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api'

// 通用请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  // 如果有token，添加到请求头
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers = {
      ...config.headers,
      Authorization: `Bearer ${token}`,
    }
  }

  try {
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error('API Request failed:', error)
    throw error
  }
}

// 商品相关API
export const productAPI = {
  // 获取商品列表
  getProducts: async (params?: {
    page?: number
    limit?: number
    category?: string
    search?: string
  }) => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.category) searchParams.append('category', params.category)
    if (params?.search) searchParams.append('search', params.search)
    
    const query = searchParams.toString()
    return apiRequest(`/products${query ? `?${query}` : ''}`)
  },

  // 获取单个商品详情
  getProduct: async (id: number) => {
    return apiRequest(`/products/${id}`)
  },

  // 创建商品（管理员功能）
  createProduct: async (productData: any) => {
    return apiRequest('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    })
  },

  // 更新商品（管理员功能）
  updateProduct: async (id: number, productData: any) => {
    return apiRequest(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    })
  },

  // 删除商品（管理员功能）
  deleteProduct: async (id: number) => {
    return apiRequest(`/products/${id}`, {
      method: 'DELETE',
    })
  },
}

// 博客相关API
export const blogAPI = {
  // 获取博客文章列表
  getPosts: async (params?: {
    page?: number
    limit?: number
    category?: string
    featured?: boolean
  }) => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.category) searchParams.append('category', params.category)
    if (params?.featured !== undefined) searchParams.append('featured', params.featured.toString())
    
    const query = searchParams.toString()
    return apiRequest(`/posts${query ? `?${query}` : ''}`)
  },

  // 获取单篇文章详情
  getPost: async (id: number) => {
    return apiRequest(`/posts/${id}`)
  },

  // 创建文章（管理员功能）
  createPost: async (postData: any) => {
    return apiRequest('/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    })
  },

  // 更新文章（管理员功能）
  updatePost: async (id: number, postData: any) => {
    return apiRequest(`/posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    })
  },

  // 删除文章（管理员功能）
  deletePost: async (id: number) => {
    return apiRequest(`/posts/${id}`, {
      method: 'DELETE',
    })
  },
}

// 用户相关API
export const userAPI = {
  // 获取用户信息
  getProfile: async () => {
    return apiRequest('/user/profile')
  },

  // 更新用户信息
  updateProfile: async (userData: any) => {
    return apiRequest('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    })
  },

  // 获取用户订单
  getOrders: async (params?: {
    page?: number
    limit?: number
    status?: string
  }) => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.status) searchParams.append('status', params.status)
    
    const query = searchParams.toString()
    return apiRequest(`/user/orders${query ? `?${query}` : ''}`)
  },
}

// 订单相关API
export const orderAPI = {
  // 创建订单
  createOrder: async (orderData: any) => {
    return apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  },

  // 获取订单详情
  getOrder: async (id: number) => {
    return apiRequest(`/orders/${id}`)
  },

  // 更新订单状态
  updateOrderStatus: async (id: number, status: string) => {
    return apiRequest(`/orders/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    })
  },
}

// 认证相关API（已在useAuth中实现，这里作为备用）
export const authAPI = {
  // 登录
  login: async (credentials: { email: string; password: string }) => {
    return apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  },

  // 注册
  register: async (userData: {
    email: string
    username: string
    password: string
  }) => {
    return apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  },

  // 刷新token
  refreshToken: async () => {
    return apiRequest('/auth/refresh', {
      method: 'POST',
    })
  },

  // 登出
  logout: async () => {
    return apiRequest('/auth/logout', {
      method: 'POST',
    })
  },
}

export default {
  productAPI,
  blogAPI,
  userAPI,
  orderAPI,
  authAPI,
}
