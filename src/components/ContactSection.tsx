'use client'

import React from 'react'
import { Mail, MessageCircle, Phone, MapPin, Clock, Send } from 'lucide-react'

export default function ContactSection() {
  return (
    <section className="py-16 bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* 联系信息 */}
          <div>
            <h2 className="text-3xl font-bold mb-8">联系我们</h2>
            <p className="text-gray-300 text-lg mb-8 leading-relaxed">
              有任何问题或建议？我们很乐意听到您的声音。
              通过以下方式与我们取得联系，我们会尽快回复您。
            </p>
            
            <div className="space-y-6">
              <div className="contact-item">
                <div className="contact-icon">
                  <Mail size={20} />
                </div>
                <div>
                  <h3 className="contact-title">邮箱联系</h3>
                  <p className="contact-info"><EMAIL></p>
                  <p className="contact-desc">我们会在24小时内回复您的邮件</p>
                </div>
              </div>
              
              <div className="contact-item">
                <div className="contact-icon">
                  <MessageCircle size={20} />
                </div>
                <div>
                  <h3 className="contact-title">在线客服</h3>
                  <p className="contact-info">微信：Station_Support</p>
                  <p className="contact-desc">工作日 9:00-18:00 在线服务</p>
                </div>
              </div>
              
              <div className="contact-item">
                <div className="contact-icon">
                  <Phone size={20} />
                </div>
                <div>
                  <h3 className="contact-title">电话咨询</h3>
                  <p className="contact-info">************</p>
                  <p className="contact-desc">周一至周五 9:00-18:00</p>
                </div>
              </div>
              
              <div className="contact-item">
                <div className="contact-icon">
                  <MapPin size={20} />
                </div>
                <div>
                  <h3 className="contact-title">办公地址</h3>
                  <p className="contact-info">北京市朝阳区创意大厦 808</p>
                  <p className="contact-desc">欢迎预约到访交流</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* 联系方式说明 */}
          <div>
            <div className="bg-gray-800 rounded-2xl p-8">
              <h3 className="text-2xl font-bold mb-6">联系我们</h3>
              <p className="text-gray-300 mb-6 leading-relaxed">
                如需更详细的咨询或合作洽谈，请通过左侧提供的联系方式与我们取得联系。
                我们将竭诚为您提供专业的服务和支持。
              </p>

              {/* 工作时间提示 */}
              <div className="p-4 bg-primary-900 bg-opacity-50 rounded-lg">
                <div className="flex items-center">
                  <Clock size={16} className="text-primary-400 mr-2" />
                  <span className="text-sm text-gray-300">
                    工作时间：周一至周五 9:00-18:00，我们会尽快回复您的消息
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 底部信息 */}
        <div className="mt-16 pt-8 border-t border-gray-800">
          <div className="text-center">
            <p className="text-gray-400 mb-4">
              © 2024 Station. All rights reserved.
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <a href="#" className="hover:text-primary-400 transition-colors">隐私政策</a>
              <a href="#" className="hover:text-primary-400 transition-colors">服务条款</a>
              <a href="#" className="hover:text-primary-400 transition-colors">关于我们</a>
              <a href="/helps" className="hover:text-primary-400 transition-colors">帮助中心</a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
