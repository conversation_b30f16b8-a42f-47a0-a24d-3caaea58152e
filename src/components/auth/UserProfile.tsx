'use client'

import React, { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Input, Button, Alert } from '@/components/ui'
import { User, Mail, Calendar, Shield, Edit3, Save, X } from 'lucide-react'
import { isValidUsername } from '@/utils/validation'

interface ProfileForm {
  username: string
  email: string
}

const UserProfile: React.FC = () => {
  const { user, refreshUser } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [form, setForm] = useState<ProfileForm>({
    username: user?.username || '',
    email: user?.email || ''
  })
  const [errors, setErrors] = useState<{
    username?: string
    email?: string
  }>({})
  const [alert, setAlert] = useState<{
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
  } | null>(null)

  if (!user) return null

  const handleInputChange = (field: keyof ProfileForm) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setForm(prev => ({
      ...prev,
      [field]: e.target.value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { username?: string; email?: string } = {}

    if (!form.username) {
      newErrors.username = '请输入用户名'
    } else if (!isValidUsername(form.username)) {
      newErrors.username = '用户名格式不正确（3-20位字母、数字、下划线）'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setLoading(true)
    setAlert(null)

    try {
      // 这里应该调用更新用户信息的API
      // const response = await fetch('/api/user/profile', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(form)
      // })

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      setAlert({
        type: 'success',
        message: '个人信息更新成功'
      })
      
      setIsEditing(false)
      await refreshUser()
    } catch (error) {
      console.error('更新用户信息错误:', error)
      setAlert({
        type: 'error',
        message: '更新失败，请稍后重试'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setForm({
      username: user.username,
      email: user.email
    })
    setErrors({})
    setIsEditing(false)
    setAlert(null)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getRoleName = (role: string) => {
    const roleNames = {
      'user': '普通用户',
      'admin': '管理员'
    }
    return roleNames[role as keyof typeof roleNames] || role
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">个人资料</h2>
            {!isEditing && (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
                size="sm"
                icon={<Edit3 size={16} />}
              >
                编辑资料
              </Button>
            )}
          </div>
        </div>

        <div className="card-body">
          {alert && (
            <div className="mb-6">
              <Alert
                type={alert.type}
                message={alert.message}
                dismissible
                onDismiss={() => setAlert(null)}
              />
            </div>
          )}

          <div className="space-y-6">
            {/* 头像区域 */}
            <div className="flex items-center gap-6">
              <div className="user-avatar w-20 h-20">
                {user.avatar_url ? (
                  <img src={user.avatar_url} alt="头像" className="avatar-img" />
                ) : (
                  <div className="avatar-placeholder">
                    <User size={32} />
                  </div>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {user.username}
                </h3>
                <p className="text-gray-600">{user.email}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Shield size={16} className="text-primary-600" />
                  <span className="text-sm text-gray-600">
                    {getRoleName(user.role)}
                  </span>
                </div>
              </div>
            </div>

            {/* 表单区域 */}
            <div className="space-y-4">
              <Input
                label="用户名"
                value={form.username}
                onChange={handleInputChange('username')}
                error={errors.username}
                icon={<User size={20} />}
                disabled={!isEditing}
                className={isEditing ? '' : 'bg-gray-50'}
              />

              <Input
                label="邮箱地址"
                value={form.email}
                disabled={true}
                icon={<Mail size={20} />}
                className="bg-gray-50"
                helpText="邮箱地址不可修改"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label className="form-label">注册时间</label>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <Calendar size={20} className="text-gray-400" />
                    <span className="text-gray-700">
                      {formatDate(user.created_at)}
                    </span>
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">账户状态</label>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className={`status-dot ${user.is_active ? 'online' : 'offline'}`}></div>
                    <span className="text-gray-700">
                      {user.is_active ? '正常' : '已禁用'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            {isEditing && (
              <div className="flex gap-3 pt-4 border-t border-gray-200">
                <Button
                  onClick={handleSave}
                  loading={loading}
                  icon={<Save size={16} />}
                >
                  {loading ? '保存中...' : '保存更改'}
                </Button>
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  icon={<X size={16} />}
                >
                  取消
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserProfile
