'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Train } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
  fallback?: React.ReactNode
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  requireAdmin = false,
  redirectTo = '/login',
  fallback
}) => {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      // 需要登录但用户未登录
      if (requireAuth && !user) {
        router.push(redirectTo)
        return
      }

      // 需要管理员权限但用户不是管理员
      if (requireAdmin && user && user.role !== 'admin') {
        router.push('/')
        return
      }
    }
  }, [user, loading, requireAuth, requireAdmin, redirectTo, router])

  // 显示加载状态
  if (loading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-white to-primary-100">
          <div className="text-center">
            <Train size={48} className="mx-auto text-primary-600 mb-4 animate-pulse" />
            <div className="spinner mx-auto mb-4"></div>
            <p className="text-gray-600">验证身份中...</p>
          </div>
        </div>
      )
    )
  }

  // 需要登录但用户未登录
  if (requireAuth && !user) {
    return null // 将重定向到登录页面
  }

  // 需要管理员权限但用户不是管理员
  if (requireAdmin && user && user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-white to-primary-100">
        <div className="text-center max-w-md p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Train size={32} className="text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">访问受限</h2>
          <p className="text-gray-600 mb-6">
            抱歉，您没有权限访问此页面。此页面仅限管理员访问。
          </p>
          <button
            onClick={() => router.push('/')}
            className="btn btn-primary"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

export default AuthGuard
