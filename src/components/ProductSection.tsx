'use client'

import React from 'react'
import Link from 'next/link'
import { ShoppingBag, ArrowRight, Heart } from 'lucide-react'
import { Product } from '@/types'

// 商品数据 - 待对接后端API
const mockProducts: Product[] = [
  // 暂时为空，等待后端API对接
]

export default function ProductSection() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <ShoppingBag className="text-primary-600 mr-3" size={32} />
            <h2 className="text-3xl font-bold text-gray-900">精选商品</h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            精心挑选的优质商品，为您的生活带来更多便利与美好
          </p>
        </div>

        {/* 商品网格 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {mockProducts.length > 0 ? (
            mockProducts.map((product) => (
              <div key={product.id} className="product-card group">
                <div className="product-image-container">
                  <div className="product-image-placeholder">
                    <ShoppingBag size={48} className="text-gray-400" />
                  </div>
                  {product.status === 'active' && (
                    <span className="product-tag tag-active">
                      在售
                    </span>
                  )}
                  <button className="product-favorite">
                    <Heart size={16} />
                  </button>
                </div>

                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  <p className="product-description">{product.description || '暂无描述'}</p>

                  <div className="product-stock">
                    <span className="text-sm text-gray-500">
                      库存: {product.stock}
                    </span>
                  </div>

                  <div className="product-price">
                    <span className="current-price">¥{product.price}</span>
                  </div>

                  <button className="product-buy-btn">
                    <ShoppingBag size={16} />
                    立即购买
                  </button>
                </div>
              </div>
            ))
          ) : (
            // 空状态展示
            <div className="col-span-full empty-state">
              <ShoppingBag size={64} className="empty-state-icon" />
              <h3 className="empty-state-title">暂无商品</h3>
              <p className="empty-state-description">商品数据正在准备中，敬请期待...</p>
            </div>
          )}
        </div>

        {/* 查看更多按钮 */}
        <div className="text-center">
          <Link href="/commodity" className="btn btn-outline btn-lg">
            查看更多商品
            <ArrowRight size={20} />
          </Link>
        </div>
      </div>
    </section>
  )
}
