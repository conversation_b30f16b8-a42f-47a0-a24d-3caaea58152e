'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { 
  ShoppingBag, 
  BookOpen, 
  HelpCircle, 
  Ticket, 
  Menu, 
  X,
  Train,
  LogOut,
  User
} from 'lucide-react'

export default function Navbar() {
  const { user, loading, logout } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navItems = [
    { name: '商品广场', href: '/commodity', icon: ShoppingBag },
    { name: '站长博客', href: '/books', icon: BookOpen },
    { name: '帮助中心', href: '/helps', icon: HelpCircle },
  ]

  const userNavItems = user ? [
    { name: '我的车票', href: '/ticket-user', icon: Ticket },
  ] : []

  return (
    <nav className="nav">
      <div className="nav-container">
        <div className="nav-content">
          <div className="flex items-center">
            <Link href="/" className="logo">
              <Train className="station-icon" size={28} />
              Station
            </Link>
          </div>

          <div className="flex items-center space-x-6">
            <div className="hidden md-flex items-center space-x-6">
              {navItems.map((item) => {
                const IconComponent = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="nav-link"
                  >
                    <IconComponent className="nav-icon" size={18} />
                    {item.name}
                  </Link>
                )
              })}
              
              {userNavItems.map((item) => {
                const IconComponent = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="nav-link"
                  >
                    <IconComponent className="nav-icon" size={18} />
                    {item.name}
                  </Link>
                )
              })}
            </div>

            <div className="flex items-center space-x-4">
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="spinner w-4 h-4"></div>
                  <span className="text-gray-500 text-sm">加载中...</span>
                </div>
              ) : user ? (
                <div className="flex items-center space-x-4">
                  <div className="user-info">
                    <span className="user-avatar">
                      {user.avatar_url ? (
                        <img src={user.avatar_url} alt="头像" className="avatar-img" />
                      ) : (
                        <div className="avatar-placeholder">
                          <User size={16} />
                        </div>
                      )}
                    </span>
                    <span className="hidden sm-inline text-gray-700 font-medium">
                      {user.username}
                    </span>
                  </div>
                  <button
                    onClick={logout}
                    className="btn btn-secondary btn-sm"
                    title="登出"
                  >
                    <LogOut size={16} />
                    <span className="hidden sm:inline">登出</span>
                  </button>
                </div>
              ) : (
                <div className="flex items-center space-x-3">
                  <Link href="/login" className="btn btn-ghost btn-sm">
                    登录
                  </Link>
                  <Link href="/register" className="btn btn-primary btn-sm">
                    注册
                  </Link>
                </div>
              )}

              <button
                className="md-hidden menu-toggle"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-label="切换菜单"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>

          {isMenuOpen && (
            <div className="mobile-menu">
              <div className="mobile-menu-content">
                {navItems.map((item) => {
                  const IconComponent = item.icon
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="mobile-nav-link"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <IconComponent className="nav-icon" size={20} />
                      {item.name}
                    </Link>
                  )
                })}
                
                {userNavItems.map((item) => {
                  const IconComponent = item.icon
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="mobile-nav-link"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <IconComponent className="nav-icon" size={20} />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
