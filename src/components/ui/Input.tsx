'use client'

import React, { forwardRef, useState } from 'react'
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  success?: string
  helpText?: string
  required?: boolean
  showPasswordToggle?: boolean
  icon?: React.ReactNode
  variant?: 'default' | 'filled' | 'outlined'
  size?: 'sm' | 'md' | 'lg'
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className = '',
      label,
      error,
      success,
      helpText,
      required = false,
      showPasswordToggle = false,
      icon,
      variant = 'default',
      size = 'md',
      type = 'text',
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)

    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : type

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg'
    }

    const variantClasses = {
      default: 'border border-gray-300 bg-white',
      filled: 'border-0 bg-gray-100',
      outlined: 'border-2 border-gray-300 bg-transparent'
    }

    const getInputClasses = () => {
      let classes = `
        w-full rounded-lg transition-all duration-200 ease-in-out
        placeholder:text-gray-400 focus:outline-none
        ${sizeClasses[size]}
        ${variantClasses[variant]}
      `

      if (error) {
        classes += ' border-red-500 bg-red-50 focus:border-red-600 focus:ring-2 focus:ring-red-200'
      } else if (success) {
        classes += ' border-green-500 bg-green-50 focus:border-green-600 focus:ring-2 focus:ring-green-200'
      } else {
        classes += ' focus:border-primary-500 focus:ring-2 focus:ring-primary-200'
      }

      if (disabled) {
        classes += ' opacity-50 cursor-not-allowed bg-gray-100'
      }

      if (icon || showPasswordToggle) {
        classes += icon ? ' pl-12' : ''
        classes += showPasswordToggle ? ' pr-12' : ''
      }

      return classes + ' ' + className
    }

    return (
      <div className="form-group">
        {label && (
          <label className={`form-label ${required ? 'required' : ''}`}>
            {label}
          </label>
        )}
        
        <div className="relative">
          {icon && (
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
              {icon}
            </div>
          )}
          
          <input
            ref={ref}
            type={inputType}
            className={getInputClasses()}
            disabled={disabled}
            onFocus={(e) => {
              setIsFocused(true)
              props.onFocus?.(e)
            }}
            onBlur={(e) => {
              setIsFocused(false)
              props.onBlur?.(e)
            }}
            {...props}
          />
          
          {showPasswordToggle && type === 'password' && (
            <button
              type="button"
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          )}
          
          {(error || success) && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
              {error && <AlertCircle size={20} className="text-red-500" />}
              {success && <CheckCircle size={20} className="text-green-500" />}
            </div>
          )}
        </div>
        
        {error && <span className="form-error">{error}</span>}
        {success && <span className="form-success">{success}</span>}
        {helpText && !error && !success && (
          <span className="form-help">{helpText}</span>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export default Input
