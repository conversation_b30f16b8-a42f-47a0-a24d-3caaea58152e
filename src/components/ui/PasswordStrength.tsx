'use client'

import React from 'react'
import { Check, X } from 'lucide-react'

interface PasswordStrengthProps {
  password: string
  showRequirements?: boolean
}

interface PasswordRequirement {
  label: string
  test: (password: string) => boolean
}

const passwordRequirements: PasswordRequirement[] = [
  {
    label: '至少8个字符',
    test: (password) => password.length >= 8
  },
  {
    label: '包含大写字母',
    test: (password) => /[A-Z]/.test(password)
  },
  {
    label: '包含小写字母',
    test: (password) => /[a-z]/.test(password)
  },
  {
    label: '包含数字',
    test: (password) => /\d/.test(password)
  },
  {
    label: '包含特殊字符',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
]

const getPasswordStrength = (password: string): {
  score: number
  level: 'weak' | 'fair' | 'good' | 'strong'
  color: string
} => {
  if (!password) return { score: 0, level: 'weak', color: 'bg-gray-200' }
  
  const passedRequirements = passwordRequirements.filter(req => req.test(password)).length
  const score = (passedRequirements / passwordRequirements.length) * 100
  
  if (score < 40) return { score, level: 'weak', color: 'bg-red-500' }
  if (score < 60) return { score, level: 'fair', color: 'bg-yellow-500' }
  if (score < 80) return { score, level: 'good', color: 'bg-blue-500' }
  return { score, level: 'strong', color: 'bg-green-500' }
}

const PasswordStrength: React.FC<PasswordStrengthProps> = ({ 
  password, 
  showRequirements = true 
}) => {
  const strength = getPasswordStrength(password)
  
  const strengthLabels = {
    weak: '弱',
    fair: '一般',
    good: '良好',
    strong: '强'
  }
  
  if (!password) return null
  
  return (
    <div className="mt-3 space-y-3">
      {/* 强度条 */}
      <div className="space-y-2">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">密码强度</span>
          <span className={`font-medium ${
            strength.level === 'weak' ? 'text-red-600' :
            strength.level === 'fair' ? 'text-yellow-600' :
            strength.level === 'good' ? 'text-blue-600' :
            'text-green-600'
          }`}>
            {strengthLabels[strength.level]}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div
            className={`h-full transition-all duration-500 ease-out password-strength-bar ${strength.color}`}
            style={{ width: `${strength.score}%` }}
          />
        </div>
      </div>
      
      {/* 要求列表 */}
      {showRequirements && (
        <div className="space-y-2">
          <p className="text-sm text-gray-600 font-medium">密码要求：</p>
          <div className="grid grid-cols-1 gap-1">
            {passwordRequirements.map((requirement, index) => {
              const isPassed = requirement.test(password)
              return (
                <div
                  key={index}
                  className={`flex items-center gap-2 text-xs transition-colors duration-200 ${
                    isPassed ? 'text-green-600' : 'text-gray-500'
                  }`}
                >
                  <div className={`flex-shrink-0 w-4 h-4 rounded-full flex items-center justify-center transition-colors duration-200 ${
                    isPassed ? 'bg-green-100' : 'bg-gray-100'
                  }`}>
                    {isPassed ? (
                      <Check size={10} className="text-green-600" />
                    ) : (
                      <X size={10} className="text-gray-400" />
                    )}
                  </div>
                  <span>{requirement.label}</span>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default PasswordStrength
