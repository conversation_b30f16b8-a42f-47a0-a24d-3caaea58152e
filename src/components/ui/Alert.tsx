'use client'

import React from 'react'
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react'

export interface AlertProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const Alert: React.FC<AlertProps> = ({
  type = 'info',
  title,
  message,
  dismissible = false,
  onDismiss,
  className = ''
}) => {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertCircle,
    info: Info
  }
  
  const styles = {
    success: {
      container: 'bg-green-50 border-green-200 text-green-800',
      icon: 'text-green-500',
      title: 'text-green-800',
      message: 'text-green-700'
    },
    error: {
      container: 'bg-red-50 border-red-200 text-red-800',
      icon: 'text-red-500',
      title: 'text-red-800',
      message: 'text-red-700'
    },
    warning: {
      container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: 'text-yellow-500',
      title: 'text-yellow-800',
      message: 'text-yellow-700'
    },
    info: {
      container: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: 'text-blue-500',
      title: 'text-blue-800',
      message: 'text-blue-700'
    }
  }
  
  const Icon = icons[type]
  const style = styles[type]
  
  return (
    <div className={`
      relative border rounded-lg p-4 transition-all duration-200 ease-in-out
      ${style.container} ${className}
    `}>
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 ${style.icon}`}>
          <Icon size={20} />
        </div>
        
        <div className="flex-1 min-w-0">
          {title && (
            <h4 className={`font-medium text-sm mb-1 ${style.title}`}>
              {title}
            </h4>
          )}
          <p className={`text-sm leading-relaxed ${style.message}`}>
            {message}
          </p>
        </div>
        
        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className={`
              flex-shrink-0 p-1 rounded-md transition-colors duration-200
              hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2
              ${type === 'success' ? 'focus:ring-green-500' :
                type === 'error' ? 'focus:ring-red-500' :
                type === 'warning' ? 'focus:ring-yellow-500' :
                'focus:ring-blue-500'}
            `}
          >
            <X size={16} className={style.icon} />
          </button>
        )}
      </div>
    </div>
  )
}

export default Alert
