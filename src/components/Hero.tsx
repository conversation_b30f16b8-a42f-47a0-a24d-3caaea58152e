'use client'

import React from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { ShoppingBag, BookOpen, Ticket, Target, Sparkles, ArrowRight } from 'lucide-react'

export default function Hero() {
  const { user } = useAuth()

  return (
    <section className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              欢迎来到 <span className="station-highlight">Station</span>
            </h1>
            <p className="hero-description">
              一个集商品销售和博客于一体的个人站点
            </p>
            <p className="hero-subtitle">
              在这里，您可以发现精选商品，阅读精彩文章，享受独特的购物和阅读体验
            </p>
          </div>

          {user ? (
            <div className="hero-actions">
              <div className="welcome-back">
                <h2 className="welcome-title">
                  欢迎回来，{user.username}！
                </h2>
                <p className="welcome-subtitle">
                  继续您的 Station 之旅
                </p>
              </div>
              
              <div className="action-cards">
                <Link href="/commodity" className="action-card card-interactive">
                  <ShoppingBag className="action-icon" size={48} />
                  <h3 className="action-title">商品广场</h3>
                  <p className="action-description">
                    浏览精选商品，发现心仪好物
                  </p>
                  <div className="action-arrow">
                    <ArrowRight size={16} />
                  </div>
                </Link>

                <Link href="/books" className="action-card card-interactive">
                  <BookOpen className="action-icon" size={48} />
                  <h3 className="action-title">站长博客</h3>
                  <p className="action-description">
                    阅读最新文章，获取灵感启发
                  </p>
                  <div className="action-arrow">
                    <ArrowRight size={16} />
                  </div>
                </Link>

                <Link href="/ticket-user" className="action-card card-interactive">
                  <Ticket className="action-icon" size={48} />
                  <h3 className="action-title">我的车票</h3>
                  <p className="action-description">
                    查看订单历史，管理购买记录
                  </p>
                  <div className="action-arrow">
                    <ArrowRight size={16} />
                  </div>
                </Link>
              </div>
            </div>
          ) : (
            <div className="hero-actions">
              <div className="cta-section">
                <h2 className="cta-title">
                  开始您的 Station 之旅
                </h2>
                <p className="cta-subtitle">
                  注册账户，解锁完整功能体验
                </p>
                <div className="cta-buttons">
                  <Link href="/register" className="btn btn-primary btn-xl btn-round">
                    <Sparkles size={20} />
                    立即注册
                  </Link>
                  <Link href="/login" className="btn btn-outline btn-xl btn-round">
                    已有账户？登录
                  </Link>
                </div>
              </div>
              
              <div className="preview-cards">
                <div className="preview-card">
                  <ShoppingBag className="preview-icon" size={40} />
                  <h3 className="preview-title">精选商品</h3>
                  <p className="preview-description">
                    发现独特好物，享受优质购物体验
                  </p>
                </div>

                <div className="preview-card">
                  <BookOpen className="preview-icon" size={40} />
                  <h3 className="preview-title">精彩博客</h3>
                  <p className="preview-description">
                    阅读深度文章，获取知识与灵感
                  </p>
                </div>

                <div className="preview-card">
                  <Target className="preview-icon" size={40} />
                  <h3 className="preview-title">个性化体验</h3>
                  <p className="preview-description">
                    定制专属内容，打造个人空间
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
