'use client'

import React from 'react'
import Link from 'next/link'
import { Book<PERSON><PERSON>, ArrowRight, Calendar, User, Eye } from 'lucide-react'
import { BlogPost } from '@/types'

// 博客数据 - 待对接后端API
const mockBlogs: BlogPost[] = [
  // 暂时为空，等待后端API对接
]

export default function BlogSection() {
  const featuredPost = mockBlogs.find(post => post.status === 'published')
  const regularPosts = mockBlogs.filter(post => post.status === 'published')

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <BookOpen className="text-primary-600 mr-3" size={32} />
            <h2 className="text-3xl font-bold text-gray-900">站长博客</h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            分享生活感悟、创意思考和品质生活的点点滴滴
          </p>
        </div>

        {mockBlogs.length > 0 ? (
          <>
            {/* 特色文章 */}
            {featuredPost && (
              <div className="featured-post mb-12">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                  <div className="md:flex">
                    <div className="md:w-1/2">
                      <div className="featured-image-placeholder">
                        <BookOpen size={64} className="text-gray-400" />
                      </div>
                    </div>
                    <div className="md:w-1/2 p-8">
                      <div className="flex items-center mb-4">
                        <span className="badge badge-primary">特色文章</span>
                        <span className="badge badge-success ml-2">{featuredPost.category?.name || '未分类'}</span>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        {featuredPost.title}
                      </h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {featuredPost.excerpt || '暂无摘要'}
                      </p>
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <User size={14} className="mr-1" />
                            站长
                          </div>
                          <div className="flex items-center">
                            <Calendar size={14} className="mr-1" />
                            {featuredPost.published_at || featuredPost.created_at}
                          </div>
                          <div className="flex items-center">
                            <Eye size={14} className="mr-1" />
                            阅读
                          </div>
                        </div>
                      </div>
                      <Link href={`/books/${featuredPost.id}`} className="btn btn-primary">
                        阅读全文
                        <ArrowRight size={16} />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 常规文章网格 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {regularPosts.map((post) => (
                <article key={post.id} className="blog-card">
                  <div className="blog-image-placeholder">
                    <BookOpen size={40} className="text-gray-400" />
                  </div>

                  <div className="blog-content">
                    <div className="flex items-center justify-between mb-3">
                      <span className="blog-category">{post.category?.name || '未分类'}</span>
                      <span className="blog-read-time">阅读</span>
                    </div>

                    <h3 className="blog-title">
                      <Link href={`/books/${post.id}`}>
                        {post.title}
                      </Link>
                    </h3>

                    <p className="blog-excerpt">
                      {post.excerpt || '暂无摘要'}
                    </p>

                    <div className="blog-meta">
                      <div className="flex items-center space-x-3 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar size={12} className="mr-1" />
                          {post.published_at || post.created_at}
                        </div>
                        <div className="flex items-center">
                          <Eye size={12} className="mr-1" />
                          查看
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </>
        ) : (
          // 空状态展示
          <div className="empty-state">
            <BookOpen size={64} className="empty-state-icon" />
            <h3 className="empty-state-title">暂无文章</h3>
            <p className="empty-state-description">博客内容正在准备中，敬请期待...</p>
          </div>
        )}

        {/* 查看更多按钮 */}
        <div className="text-center">
          <Link href="/books" className="btn btn-outline btn-lg">
            查看更多文章
            <ArrowRight size={20} />
          </Link>
        </div>
      </div>
    </section>
  )
}
