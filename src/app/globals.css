:root {
  /* 主色调 - 青蓝色系 */
  --primary-50: #ecfeff;
  --primary-100: #cffafe;
  --primary-200: #a5f3fc;
  --primary-300: #67e8f9;
  --primary-400: #22d3ee;
  --primary-500: #06b6d4;
  --primary-600: #0891b2;
  --primary-700: #0e7490;
  --primary-800: #155e75;
  --primary-900: #164e63;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 语义化颜色 */
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --error: #ef4444;
  --error-light: #fee2e2;
  --info: #3b82f6;
  --info-light: #dbeafe;

  /* 背景和前景 */
  --background: #ffffff;
  --background-secondary: var(--gray-50);
  --foreground: var(--gray-900);
  --foreground-secondary: var(--gray-600);

  /* 主要颜色 */
  --primary: var(--primary-600);
  --primary-hover: var(--primary-700);
  --primary-light: var(--primary-100);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 边框半径 */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  font-size: 16px;
  overflow-x: hidden;
}

/* 改进的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 选择文本样式 */
::selection {
  background: var(--primary-200);
  color: var(--primary-900);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 字体大小系统 */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

/* 字体粗细 */
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* 基础样式 */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

/* 按钮样式系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-200);
}

/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--background);
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: var(--primary-600);
  border-color: var(--primary-300);
}

.btn-outline:hover {
  background: var(--primary-50);
  border-color: var(--primary-400);
  color: var(--primary-700);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border: none;
  box-shadow: none;
}

.btn-ghost:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

/* 按钮尺寸 */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

/* 全宽按钮 */
.btn-full {
  width: 100%;
}

/* 圆形按钮 */
.btn-round {
  border-radius: 9999px;
}

/* 按钮加载状态 */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 表单和输入框样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--error);
}

.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 1rem;
  line-height: 1.5;
  background: var(--background);
  color: var(--foreground);
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.input::placeholder {
  color: var(--gray-400);
}

.input:hover {
  border-color: var(--gray-400);
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-200);
  background: var(--background);
}

.input:disabled {
  background: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

/* 输入框状态 */
.input.error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px var(--error-light);
}

.input.success {
  border-color: var(--success);
  box-shadow: 0 0 0 3px var(--success-light);
}

/* 输入框尺寸 */
.input-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.input-lg {
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
}

/* 表单错误信息 */
.form-error {
  display: block;
  font-size: 0.75rem;
  color: var(--error);
  margin-top: 0.25rem;
}

.form-success {
  display: block;
  font-size: 0.75rem;
  color: var(--success);
  margin-top: 0.25rem;
}

/* 表单帮助文本 */
.form-help {
  display: block;
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 0.25rem;
}

/* 卡片样式系统 */
.card {
  background: var(--background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-300);
}

.card-interactive:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 卡片内容区域 */
.card-header {
  padding: 1.5rem 1.5rem 0;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 0 1.5rem 1.5rem;
  border-top: 1px solid var(--gray-200);
  margin-top: 1rem;
  padding-top: 1rem;
}

/* 卡片变体 */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-outlined {
  box-shadow: none;
  border: 2px solid var(--gray-200);
}

.card-ghost {
  background: transparent;
  box-shadow: none;
  border: 1px dashed var(--gray-300);
}

/* 卡片尺寸 */
.card-sm {
  border-radius: var(--radius);
}

.card-sm .card-header,
.card-sm .card-body,
.card-sm .card-footer {
  padding: 1rem;
}

.card-lg {
  border-radius: var(--radius-xl);
}

.card-lg .card-header,
.card-lg .card-body,
.card-lg .card-footer {
  padding: 2rem;
}

/* 导航栏样式 */
.nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 50;
  transition: all var(--transition);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all var(--transition);
}

.logo:hover {
  transform: scale(1.05);
}

.station-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

/* 导航链接样式 */
.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: -1;
}

.nav-link:hover {
  color: var(--primary-700);
  transform: translateY(-1px);
}

.nav-link:hover::before {
  opacity: 1;
}

.nav-link:active {
  transform: translateY(0);
}

.nav-icon {
  font-size: 1.125rem;
  transition: transform var(--transition);
}

.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: var(--radius-lg);
  transition: all var(--transition);
}

.user-info:hover {
  background: var(--gray-50);
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
  transition: all var(--transition);
}

.user-avatar:hover {
  box-shadow: var(--shadow-md);
  transform: scale(1.05);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

/* 移动端菜单按钮 */
.menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  transition: all var(--transition);
}

.menu-toggle:hover {
  background: var(--gray-100);
}

.hamburger {
  width: 1.5rem;
  height: 1.2rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--gray-700);
  border-radius: 1px;
  transition: all var(--transition-slow);
}

.hamburger.open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
  background-color: var(--primary-600);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
  background-color: var(--primary-600);
}

/* 移动端菜单 */
.mobile-menu {
  display: block;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  animation: slideDown var(--transition-slow) ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-content {
  padding: 1.5rem;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  margin-bottom: 0.5rem;
  font-weight: 500;
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: -1;
}

.mobile-nav-link:hover {
  color: var(--primary-700);
  transform: translateX(4px);
}

.mobile-nav-link:hover::before {
  opacity: 1;
}

.mobile-nav-link .nav-icon {
  font-size: 1.25rem;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: var(--shadow);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲加载动画 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 滑入动画 */
.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* 响应式隐藏类 */
.hidden {
  display: none;
}

@media (min-width: 768px) {
  .md-flex {
    display: flex;
  }

  .md-hidden {
    display: none;
  }
}

@media (max-width: 640px) {
  .sm-inline {
    display: inline;
  }
}

/* 主要内容样式 */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-900 {
  color: #111827;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.p-6 {
  padding: 1.5rem;
}

/* Hero 区域样式 */
.hero {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 50%, var(--background) 100%);
  position: relative;
  padding: 6rem 0;
  min-height: 80vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2306b6d4' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float-bg 20s ease-in-out infinite;
}

@keyframes float-bg {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.hero-content {
  text-align: center;
}

.hero-text {
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: 1.5rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.station-highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  display: inline-block;
}

.station-highlight::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-400), var(--primary-600));
  border-radius: 2px;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.hero-description {
  font-size: 1.5rem;
  color: var(--gray-600);
  margin-bottom: 1rem;
  font-weight: 400;
}

.hero-subtitle {
  font-size: 1.125rem;
  color: var(--gray-500);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.hero-actions {
  margin-top: 3rem;
}

/* 欢迎回来区域 */
.welcome-back {
  margin-bottom: 2rem;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.welcome-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
}

/* 行动卡片 */
.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.action-card {
  background: var(--background);
  padding: 2.5rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  transition: all var(--transition-slow);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
  group: hover;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  opacity: 0;
  transition: opacity var(--transition-slow);
  z-index: 0;
}

.action-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
}

.action-card:hover::before {
  opacity: 1;
}

.action-card > * {
  position: relative;
  z-index: 1;
}

.action-icon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: transform var(--transition);
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.75rem;
}

.action-description {
  color: var(--gray-600);
  line-height: 1.6;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.action-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--primary-100);
  border-radius: 50%;
  color: var(--primary-600);
  margin-left: auto;
  transition: all var(--transition);
  opacity: 0;
  transform: translateX(-10px);
}

.action-card:hover .action-arrow {
  opacity: 1;
  transform: translateX(0);
  background: var(--primary-600);
  color: white;
}

/* CTA 区域 */
.cta-section {
  margin-bottom: 4rem;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.cta-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin-bottom: 2.5rem;
  line-height: 1.5;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-large {
  padding: 1rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

/* 预览卡片 */
.preview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.preview-card {
  background: var(--background);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  transition: all var(--transition-slow);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.preview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--gray-50), var(--primary-50));
  opacity: 0;
  transition: opacity var(--transition);
  z-index: 0;
}

.preview-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}

.preview-card:hover::before {
  opacity: 1;
}

.preview-card > * {
  position: relative;
  z-index: 1;
}

.preview-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: transform var(--transition);
}

.preview-card:hover .preview-icon {
  transform: scale(1.1);
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.75rem;
}

.preview-description {
  color: var(--gray-600);
  line-height: 1.6;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero {
    padding: 4rem 0;
    min-height: 70vh;
  }

  .hero-title {
    font-size: 3rem;
  }

  .action-cards,
  .preview-cards {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 3rem 0;
    min-height: 60vh;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.25rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
  }

  .action-cards,
  .preview-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nav-content {
    height: 3.5rem;
  }

  .logo {
    font-size: 1.25rem;
  }

  .station-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1.125rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .cta-title {
    font-size: 1.75rem;
  }

  .action-card,
  .preview-card {
    padding: 1.5rem;
  }

  .action-icon,
  .preview-icon {
    font-size: 2.5rem;
  }

  .mobile-menu-content {
    padding: 1rem;
  }

  .mobile-nav-link {
    padding: 0.75rem;
  }
}

/* 实用工具类 */
.max-w-2xl {
  max-width: 42rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.leading-relaxed {
  line-height: 1.625;
}

/* 颜色类 */
.text-primary-500 { color: var(--primary-500); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }

/* 间距类 */
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }

.mt-4 { margin-top: 1rem; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

/* 宽度和高度 */
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }

/* 过渡效果 */
.transition-colors {
  transition: color var(--transition);
}

.transition-all {
  transition: all var(--transition);
}

/* 状态指示器 */
.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.online {
  background-color: var(--success);
}

.status-dot.offline {
  background-color: var(--gray-400);
}

.status-dot.busy {
  background-color: var(--warning);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-error {
  background-color: var(--error-light);
  color: var(--error);
}

/* 分隔线 */
.divider {
  height: 1px;
  background-color: var(--gray-200);
  margin: 1rem 0;
}

.divider-vertical {
  width: 1px;
  background-color: var(--gray-200);
  margin: 0 1rem;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--gray-900);
  color: white;
  padding: 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 商品卡片样式 */
.product-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.product-image-container {
  position: relative;
  height: 200px;
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
}

.product-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.tag-hot { background: linear-gradient(135deg, #ef4444, #dc2626); }
.tag-new { background: linear-gradient(135deg, #10b981, #059669); }
.tag-recommend { background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); }
.tag-limited { background: linear-gradient(135deg, #f59e0b, #d97706); }

.product-favorite {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-400);
  transition: all var(--transition);
  opacity: 0;
}

.product-card:hover .product-favorite {
  opacity: 1;
}

.product-favorite:hover {
  color: #ef4444;
  background: white;
  box-shadow: var(--shadow);
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.product-description {
  color: var(--gray-600);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.product-rating {
  margin-bottom: 1rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.current-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-600);
}

.original-price {
  font-size: 0.875rem;
  color: var(--gray-400);
  text-decoration: line-through;
}

.product-buy-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition);
  cursor: pointer;
}

.product-buy-btn:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 博客卡片样式 */
.blog-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.blog-image-placeholder {
  height: 180px;
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  display: flex;
  align-items: center;
  justify-content: center;
}

.blog-content {
  padding: 1.5rem;
}

.blog-category {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--primary-600);
  background: var(--primary-100);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.blog-read-time {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.blog-title {
  margin-bottom: 0.75rem;
}

.blog-title a {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  text-decoration: none;
  line-height: 1.4;
  transition: color var(--transition);
}

.blog-title a:hover {
  color: var(--primary-600);
}

.blog-excerpt {
  color: var(--gray-600);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-meta {
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

/* 特色文章样式 */
.featured-post {
  position: relative;
}

.featured-image-placeholder {
  height: 300px;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 联系方式样式 */
.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  width: 3rem;
  height: 3rem;
  background: var(--primary-600);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.contact-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
}

.contact-info {
  font-size: 1rem;
  color: var(--primary-300);
  margin-bottom: 0.25rem;
}

.contact-desc {
  font-size: 0.875rem;
  color: var(--gray-400);
}

/* 全屏登录/注册页面样式 */
.auth-page {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-50), var(--background), var(--primary-100));
  overflow: hidden;
}

.auth-left-panel {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  position: relative;
  overflow: hidden;
  height: 100vh;
}

.auth-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.auth-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
}

/* 全屏优化 */
.fullscreen-auth {
  height: 100vh;
  overflow: hidden;
}

.fullscreen-auth .auth-form-side {
  height: 100vh;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar {
  width: 4px;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-track {
  background: transparent;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 2px;
}

.fullscreen-auth .auth-form-side::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .fullscreen-auth {
    height: auto;
    min-height: 100vh;
    overflow: visible;
  }

  .fullscreen-auth .auth-form-side {
    height: auto;
    min-height: 100vh;
    overflow: visible;
  }
}

/* 最大宽度工具类 */
.max-w-7xl {
  max-width: 80rem;
}

.max-w-md {
  max-width: 28rem;
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

/* 响应式网格 */
@media (min-width: 640px) {
  .responsive-grid-sm {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .responsive-grid-md {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .responsive-flex-md {
    display: flex;
  }

  .responsive-half-md {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .responsive-grid-lg {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .responsive-flex-lg {
    display: flex;
  }

  .responsive-half-lg {
    width: 50%;
  }

  .responsive-hidden-lg {
    display: none;
  }
}

/* 间距工具类 */
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.px-12 { padding-left: 3rem; padding-right: 3rem; }

.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }

/* 背景渐变 */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-primary-50 {
  --tw-gradient-from: var(--primary-50);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(236, 254, 255, 0));
}

.via-white {
  --tw-gradient-stops: var(--tw-gradient-from), white, var(--tw-gradient-to, rgba(255, 255, 255, 0));
}

.to-primary-100 {
  --tw-gradient-to: var(--primary-100);
}

/* 文本渐变 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-transparent {
  color: transparent;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--gray-500);
}

.empty-state-icon {
  margin-bottom: 1rem;
  color: var(--gray-300);
}

.empty-state-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.empty-state-description {
  font-size: 0.875rem;
  color: var(--gray-500);
  max-width: 24rem;
  line-height: 1.5;
}

/* 全栅格跨越 */
.col-span-full {
  grid-column: 1 / -1;
}

/* 登录注册页面增强样式 */
.auth-page-container {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

.auth-left-panel {
  position: relative;
  overflow: hidden;
}

.auth-right-panel {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.auth-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.auth-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.auth-button:active {
  transform: translateY(0);
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 脉冲动画增强 */
@keyframes pulse-enhanced {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pulse-enhanced {
  animation: pulse-enhanced 2s ease-in-out infinite;
}

/* 渐变文字动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 密码强度指示器动画 */
.password-strength-bar {
  transition: width 0.5s ease-in-out, background-color 0.3s ease;
}

/* 表单验证状态 */
.input-valid {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.input-invalid {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* 加载状态动画 */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
