'use client'

import React from 'react'
import { AuthGuard, UserProfile } from '@/components/auth'
import { Navbar } from '@/components'

export default function ProfilePage() {
  return (
    <AuthGuard requireAuth={true}>
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
              <p className="text-gray-600 mt-2">管理您的个人信息和账户设置</p>
            </div>
            <UserProfile />
          </div>
        </main>
      </div>
    </AuthGuard>
  )
}
