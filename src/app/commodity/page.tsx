'use client'

import React from 'react'
import Navbar from '@/components/Navbar'
import { ShoppingBag, Construction, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function CommodityPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="main-container">
        <div className="text-center">
          <div className="fade-in">
            <div className="flex items-center justify-center mb-6">
              <ShoppingBag className="text-primary-600" size={64} />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              商品广场
            </h1>
            <p className="text-xl text-gray-600 mb-12">
              精选商品，等待您的发现
            </p>
          </div>

          <div className="card card-lg max-w-2xl mx-auto">
            <div className="card-body text-center">
              <Construction className="text-primary-500 mx-auto mb-6" size={80} />
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">功能开发中</h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                我们正在精心打造商品展示功能，为您带来更好的购物体验。
                <br />
                敬请期待！
              </p>
              <Link href="/" className="btn btn-primary btn-lg">
                <ArrowLeft size={20} />
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
