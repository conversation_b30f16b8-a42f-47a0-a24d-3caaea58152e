'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Input, But<PERSON>, Alert } from '@/components/ui'
import { Mail, Train, ArrowRight, ArrowLeft, Send } from 'lucide-react'
import { isValidEmail } from '@/utils/validation'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [sent, setSent] = useState(false)
  const [error, setError] = useState('')
  const [alert, setAlert] = useState<{
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
  } | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      setError('请输入邮箱地址')
      return
    }
    
    if (!isValidEmail(email)) {
      setError('请输入有效的邮箱地址')
      return
    }

    setLoading(true)
    setError('')
    setAlert(null)

    try {
      // 这里应该调用重置密码的API
      // const response = await fetch('/api/auth/forgot-password', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ email })
      // })
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setSent(true)
      setAlert({
        type: 'success',
        message: '重置密码邮件已发送，请检查您的邮箱'
      })
    } catch (error) {
      console.error('发送重置邮件错误:', error)
      setAlert({
        type: 'error',
        message: '发送重置邮件失败，请稍后重试'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleResend = async () => {
    setLoading(true)
    setAlert(null)

    try {
      // 重新发送邮件
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setAlert({
        type: 'success',
        message: '重置密码邮件已重新发送'
      })
    } catch (error) {
      setAlert({
        type: 'error',
        message: '重新发送失败，请稍后重试'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回登录链接 */}
        <div className="mb-8">
          <Link 
            href="/login" 
            className="inline-flex items-center gap-2 text-gray-600 hover:text-primary-600 transition-colors"
          >
            <ArrowLeft size={16} />
            <span>返回登录</span>
          </Link>
        </div>

        <div className="auth-form-container p-8">
          <div className="text-center mb-8">
            <Train size={40} className="mx-auto text-primary-600 mb-6" />
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {sent ? '邮件已发送' : '忘记密码'}
            </h2>
            <p className="text-gray-600">
              {sent 
                ? '我们已向您的邮箱发送了重置密码的链接' 
                : '输入您的邮箱地址，我们将发送重置密码的链接'
              }
            </p>
          </div>

          {alert && (
            <div className="mb-6">
              <Alert
                type={alert.type}
                message={alert.message}
                dismissible
                onDismiss={() => setAlert(null)}
              />
            </div>
          )}

          {!sent ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                label="邮箱地址"
                type="email"
                placeholder="请输入您的邮箱地址"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value)
                  if (error) setError('')
                }}
                error={error}
                icon={<Mail size={20} />}
                required
                className="auth-input"
              />

              <Button
                type="submit"
                loading={loading}
                fullWidth
                size="lg"
                className="auth-button"
                icon={<Send size={20} />}
                iconPosition="right"
              >
                {loading ? '发送中...' : '发送重置链接'}
              </Button>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="text-center p-6 bg-green-50 rounded-lg border border-green-200">
                <Send size={48} className="mx-auto text-green-500 mb-4" />
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  邮件发送成功
                </h3>
                <p className="text-green-700 text-sm leading-relaxed">
                  我们已向 <strong>{email}</strong> 发送了重置密码的链接。
                  请检查您的邮箱（包括垃圾邮件文件夹）并点击链接重置密码。
                </p>
              </div>

              <div className="text-center space-y-4">
                <p className="text-gray-600 text-sm">
                  没有收到邮件？
                </p>
                
                <Button
                  onClick={handleResend}
                  loading={loading}
                  variant="outline"
                  size="md"
                  className="mx-auto"
                >
                  {loading ? '重新发送中...' : '重新发送邮件'}
                </Button>
              </div>
            </div>
          )}

          <div className="mt-8 text-center">
            <p className="text-gray-600">
              想起密码了？{' '}
              <Link 
                href="/login" 
                className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
              >
                立即登录
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
