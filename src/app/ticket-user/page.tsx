'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import Navbar from '@/components/Navbar'
import { useRouter } from 'next/navigation'
import { Ticket, Construction, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function TicketUserPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <div className="text-lg text-gray-600 mt-4">加载中...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="main-container">
        <div className="text-center">
          <div className="fade-in">
            <div className="flex items-center justify-center mb-6">
              <Ticket className="text-primary-600" size={64} />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              我的车票
            </h1>
            <p className="text-xl text-gray-600 mb-12">
              查看您的订单历史和购买记录
            </p>
          </div>

          <div className="card card-lg max-w-2xl mx-auto">
            <div className="card-body text-center">
              <Construction className="text-primary-500 mx-auto mb-6" size={80} />
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">功能开发中</h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                我们正在开发订单管理功能，为您提供完整的购买记录查看体验。
                <br />
                敬请期待！
              </p>
              <Link href="/" className="btn btn-primary btn-lg">
                <ArrowLeft size={20} />
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
